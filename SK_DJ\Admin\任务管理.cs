﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Shikong.Pokemon2.PCG;

namespace Admin
{
    public partial class 任务管理 : Form
    {
        public List<TaskInfo> 任务列表 = new DataProcess().GetAllTaskAim();
        public 任务管理()
        {
            InitializeComponent();
        }

        private void linkLabel10_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            宠物列表 列表 = new 宠物列表();
            列表.类型 = "宠物序号";
            列表.TEXT = 宠物;
            列表.ShowDialog();
        }

        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            道具列表 列表 = new 道具列表();
            列表.TEXT = 奖励;
            列表.Show();
        }
        List<task> task = new List<task>();
        private void button1_Click(object sender, EventArgs e)
        {
            task t = new task();
            t.Type = comboBox1.Text;
            t.Num = textBox2.Text ;
            t.ID = textBox3.Text;
            task.Add(t);
           dataGridView2.DataSource = null;
            dataGridView2.DataSource = task;
            
        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            List<TaskInfo> 新列表 = new List<TaskInfo>();
            for (int i = 0; i < 任务列表.Count; i++)
            {
                if (任务列表[i].任务名.IndexOf(textBox1.Text) != -1 || textBox1.Text.Length == 0)
                {
                    新列表.Add(任务列表[i]);
                }
            }
            dataGridView1.DataSource = 新列表;
        }

        private void linkLabel2_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            task = new List<task>();
            dataGridView2.DataSource = task;
        }

        private void linkLabel3_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            怪物列表 列表 = new 怪物列表();
            列表.类型 = "怪物序号";
            列表.TEXT = textBox3;
            列表.ShowDialog();
        }

        private void linkLabel4_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            道具列表 列表 = new 道具列表();
            列表.TEXT = textBox3;
            列表.Show();
        }

        private void button2_Click(object sender, EventArgs e)
        {
            TaskInfo 任务 = new TaskInfo();
            任务.任务奖励 = 奖励.Text;
            任务.任务名 = 名字.Text;
            任务.任务目标 = task;
            任务.任务序号 = 序号.Text;
            任务.指定宠物 = 宠物.Text;
            任务.前置任务 = textBox5.Text;
            if (checkBox1.Checked) {
                任务.允许重复 = "1";
            }
            if (new DataProcess().AddTaskAim(任务,textBox4.Text)) {
                MessageBox.Show("添加任务成功!");
                task = new List<task>();
                dataGridView2.DataSource = task;
                任务列表 = new DataProcess().GetAllTaskAim();
                dataGridView1.DataSource = 任务列表;

            }
            else
            {
                MessageBox.Show("添加任务失败!序号已经存在!");
            }
        }

        private void 任务管理_Load(object sender, EventArgs e)
        {

            任务列表 = new DataProcess().GetAllTaskAim();
            dataGridView1.DataSource = 任务列表;
        }
        
    }
}
