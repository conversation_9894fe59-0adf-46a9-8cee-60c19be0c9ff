﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using 怪物信息 = Shikong.Pokemon2.PCG.MonsterInfo;
using 用户信息 = Shikong.Pokemon2.PCG.UserInfo;
using 宠物信息 = Shikong.Pokemon2.PCG.PetInfo;
using 宠物类型 = Shikong.Pokemon2.PCG.PetConfig;
using 道具信息 = Shikong.Pokemon2.PCG.PropInfo;
using 地图信息 = Shikong.Pokemon2.PCG.MapInfo;
using 装备类型 = Shikong.Pokemon2.PCG.EquipmentType;
using 装备信息 = Shikong.Pokemon2.PCG.EquipmentInfo;
using 道具类型 = Shikong.Pokemon2.PCG.PropType;
using 道具具体信息 = Shikong.Pokemon2.PCG.PropConfig;
using 数据处理 = Shikong.Pokemon2.PCG.DataProcess;
using Shikong.Pokemon2.PCG;

namespace Admin
{
    public partial class 宠物列表 : Form
    {
        public 宠物列表()
        {
            InitializeComponent();
        }

        List<宠物类型> 道具 = new 数据处理().ReadPetTypeList();
        public String 类型 = "宠物序号";
        public TextBox TEXT;
        private void 道具列表_Load(object sender, EventArgs e)
        {
       
            dataGridView1.DataSource = 道具;
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
           
           
        }

        private void dataGridView1_Click(object sender, EventArgs e)
        {
          
        }

        private void dataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
           
        }

        private void dataGridView1_CellContentDoubleClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void dataGridView1_CellMouseDoubleClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            try
            {
                if (
                    e.RowIndex > -1)
                {
                    foreach(DataGridViewRow r in dataGridView1.SelectedRows)
                    {
                        if (类型.Equals("宠物序号"))
                        {
                            TEXT.Text = r.Cells[类型].Value.ToString();
                        }
                        else
                        {
                            TEXT.Text = r.Cells[类型].Value.ToString();
                        }
                        this.Close();
                    }
                    
                }
            }
#pragma warning disable CS0168 // 声明了变量“ex”，但从未使用过
            catch (Exception ex)
#pragma warning restore CS0168 // 声明了变量“ex”，但从未使用过
            {

            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridView1.SelectedRows.Count > 0)
                {
                    String 序号 = "";
                    foreach (DataGridViewRow r in dataGridView1.SelectedRows)
                    {
                        if (类型.Equals("宠物序号"))
                        {
                            序号 = 序号 + r.Cells[类型].Value.ToString() + "|";
                        }
                        else {
                            序号 = 序号 + r.Cells[类型].Value.ToString() + "、";
                        }
                    }
                    序号 = 序号 + "|";
                    序号 = 序号.Replace( "||","");
                    序号 = 序号.Replace("、|", "");
                    TEXT.Text += "|" + 序号;
                    this.Close();
                }
            }
#pragma warning disable CS0168 // 声明了变量“ex”，但从未使用过
            catch (Exception ex)
#pragma warning restore CS0168 // 声明了变量“ex”，但从未使用过
            {

            }
        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            List<宠物类型> 新列表 = new List<宠物类型>();
            for(int i = 0; i < 道具.Count; i++)
            {
                if(道具[i].宠物名字.IndexOf(textBox1.Text)!=-1 | textBox1.Text.Length == 0)
                {
                    新列表.Add(道具[i]);
                }
            }
            dataGridView1.DataSource = 新列表;
        }
    }
}
