<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时空神殿</title>
    <link rel="stylesheet" href="css/sksd.css">
    <script src="js/jquery-1.8.3.min.js"></script>

    <style>
        /* 成长突破相关样式 */
        .tupo-bg{
            height: 255px;
            width: 617px;
            padding: 1px;
            margin: 11px 0px 0px 12px;
            display: block;
            background:url('images/chengzhangtup/bg.png') center center no-repeat;
            background-size:cover;
            position: relative;
        }
        .tupo-magic {
            position: absolute;
            left: 50%;
            top: 90px;
            width: 180px;
            height: 180px;
            margin-left: -90px;
            background: url('images/chengzhangtup/经验法阵.png') center center no-repeat;
            background-size: contain;
            z-index: 2;
            filter: alpha(opacity=60);
            opacity: 0.6;
        }
        .tupo-lv {
            position: absolute;
            left: 50%;
            top: 110px;
            width: 180px;
            margin-left: -90px;
            font-size: 36px;
            font-weight: bold;
            color: #ffb800;
            letter-spacing: 2px;
            z-index: 3;
            text-align: center;
            filter: glow(color=#ffb800, strength=2);
        }
        .tupo-btn-group {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 10px;
            width: 100%;
            text-align: center;
            z-index: 4;
            white-space: nowrap;
        }
        .tupo-btn {
            display: inline-block;
            margin: 0 28px;
            vertical-align: bottom;
        }
        /* IE6 兼容性 */
        * html .tupo-btn {
            display: inline;
            zoom: 1;
        }
        .tupo-btn img {
            width: 95px;
            height: auto;
            cursor: pointer;
            border: none;
            /* 现代浏览器点击态 */
            transition: opacity 0.1s;
        }
        .tupo-btn img:active {
            opacity: 0.6;
            filter: alpha(opacity=60); /* IE8及以下 */
        }

        .energy-popup {
            position: absolute;
            left: 215px; /* Center horizontally */
            top: -20px;  /* Adjusted for better vertical alignment */
            width: 250px;
            height: 300px;
            background: url('images/chengzhangtup/能量汇聚素材/能量汇聚交互界面.png') no-repeat;
            overflow: hidden;
        }
        .energy-popup-bg{display:none;}

        .energy-popup-close {
            position: relative;
            left: 169px;
            top: 18px;
            width: 26px;
            height: 28px;
            background: url("images/chengzhangtup/能量汇聚素材/关闭窗口 拷贝.png") no-repeat;
            cursor: pointer;
        }

        .energy-popup-content {
            position: absolute;
            left: 18px;
            top: 38px;
            width: 220px;
            font-size: 12px;
            color: #333;
        }
        .energy-row {
            margin-bottom: 8px;
            zoom: 1; /* IE7兼容 */
        }
        .energy-row:after {
            content: '';
            display: block;
            clear: both;
        }
        .energy-label {
            font-weight: bold;
            vertical-align: middle;
        }
        .energy-input {
            width: 70px;
            height: 20px;
            border: 1px solid #999;
            vertical-align: middle;
            display: inline-block;
        }
        .energy-use-item-btn {
            display: inline-block;
            width: 55px;
            height: 22px;
            background: url("images/chengzhangtup/能量汇聚素材/btn 拷贝.png") no-repeat center center;
            color: #3A1F04;
            text-align: center;
            line-height: 22px;
            font-size: 12px;
            cursor: pointer;
            vertical-align: middle;
        }

        .energy-use-mengzutupo-btn {
            display: inline-block;
            width: 76px;
            height: 22px;
            background: url('images/chengzhangtup/能量汇聚素材/btn 拷贝.png') no-repeat center center;
            color: #3A1F04;
            text-align: center;
            line-height: 22px;
            font-size: 12px;
            cursor: pointer;
            vertical-align: middle;
            margin-left: 5px;
        }

        .energy-use-chengzhangtupo-btn {
            display: inline-block;
            height: 22px;
            background: url('images/chengzhangtup/能量汇聚素材/btn 拷贝.png') no-repeat center center;
            color: #3A1F04;
            text-align: center;
            line-height: 22px;
            font-size: 12px;
            cursor: pointer;
            vertical-align: middle;
            margin-left: 5px;
        }
        * html .energy-use-chengzhangtupo-btn, * html .energy-use-mengzutupo-btn {
            display: inline;
            zoom: 1;
        }
    </style>

    <script>
        function setTab(page, t) {
            $(".tabbox").hide();
            $(".tab_" + page).show();
            $(".task_nav li").removeClass("on");
            $(t).addClass("on");
            if(page==4)window.parent.openNpc(10);
            if(page==5)window.parent.openNpc(11);

            // 如果切换到突破选项卡（tab_7），则初始化成长突破信息
            if(page == 7) {
                console.log("切换到突破选项卡，初始化成长突破信息...");
                setTimeout(function() {
                    try {
                        updateBreakthroughInfo();
                    } catch (e) {
                        console.error("初始化成长突破信息失败：", e);
                    }
                }, 100); // 延迟100ms确保页面元素已经显示
            }
        }
        var hqList = window.parent.ejson;
        var hqMax = 0;
        var thisHQindex = 0;

        function loadHq(JSON) {

            JSON = $.parseJSON(JSON);
            hqMax = 0;
            for (var key in JSON) {

                for (var j = 0; j < hqList.length; j++) {
                    if (hqList[j].魂器ID == key) {
                        hqList[j].获得状态 = true;
                        hqList[j].数据 = JSON[key];
                        hqList[j].数据.金属性 = hqList[j].元素设定.
                        默认规则[hqList[j].元素设定.属性规则.金.强化设定.指定属性配置][hqList[j].数据.元素等级.金];
                        hqList[j].数据.金效果 = hqList[j].元素设定.属性规则.金.效果;

                        hqList[j].数据.木属性 = hqList[j].元素设定.
                        默认规则[hqList[j].元素设定.属性规则.木.强化设定.指定属性配置][hqList[j].数据.元素等级.木];
                        hqList[j].数据.木效果 = hqList[j].元素设定.属性规则.木.效果;

                        hqList[j].数据.水属性 = hqList[j].元素设定.
                        默认规则[hqList[j].元素设定.属性规则.水.强化设定.指定属性配置][hqList[j].数据.元素等级.水];
                        hqList[j].数据.水效果 = hqList[j].元素设定.属性规则.水.效果;

                        hqList[j].数据.火属性 = hqList[j].元素设定.
                        默认规则[hqList[j].元素设定.属性规则.火.强化设定.指定属性配置][hqList[j].数据.元素等级.火];
                        hqList[j].数据.火效果 = hqList[j].元素设定.属性规则.火.效果;

                        hqList[j].数据.土属性 = hqList[j].元素设定.
                        默认规则[hqList[j].元素设定.属性规则.土.强化设定.指定属性配置][hqList[j].数据.元素等级.土];
                        hqList[j].数据.土效果 = hqList[j].元素设定.属性规则.土.效果;

                        hqList[j].数据.暗属性 = hqList[j].元素设定.
                        默认规则[hqList[j].元素设定.属性规则.暗.强化设定.指定属性配置][hqList[j].数据.元素等级.暗];
                        hqList[j].数据.暗效果 = hqList[j].元素设定.属性规则.暗.效果;


                        hqMax++;
                    }
                }
            }
            $(".hq_page_max").html(hqMax);
            $(".hq_page_this").html(thisHQindex + 1);
            if (hqMax <= 0) {
                $(".hqlh_img").css("background", "null");
                // $(".sb_box_2").hide();
                return;
            }
            loadHQINFO(thisHQindex);
        }
        var hq_qhInfo = "";
        var hq_zlInfo = "";
        var hq_xlInfo = "";
        var thisHQ = null;
        var hq_pj = [
            "普通",
            "精良",
            "优秀",
            "稀有",
            "史诗",
            "传说"
        ];

        function loadHQINFO(index) {
            if (index + 1 > hqMax) {
                window.parent.alert("已经是最后一页了！");
                return;
            }
            if (index < 0) {
                window.parent.alert("已经是第一页了！");
                return;
            }
            thisHQindex = index;
            $(".hq_page_this").html(thisHQindex + 1);
            var countSX = {
                命中: 0,
                攻击: 0,
                生命: 0,
                防御: 0,
                速度: 0,
                加深: 0,
                吸血: 0,
                抵消: 0,
                吸魔: 0
            };
            for (var i = 0; i < hqList.length; i++) {
                if (hqList[i].获得状态 == true) {
                    if (i == index) {
                        $(".hqlh_img").show();
                        $(".hqlh_icoList").show();
                        $(".hqlh_btnlist").show();
                        $(".sb_pageInfo").show();
                        $(".hq_page_name").show();
                        $(".hq_next").show();
                        $(".noHQ").hide();
                        thisHQID = hqList[i].魂器ID;
                        thisHQ = hqList[i];
                        var thisCT = new Array();
                        $(".hq_page_name").html(hqList[i].魂器ID);
                        if (hqList[i].数据.词条 != null) {

                            for (var ct in hqList[i].数据.词条) {
                                countSX[ct] = accAdd(countSX[ct], hqList[i].数据.词条[ct]);
                                thisCT.push(ct + "：+" + accMul(hqList[i].数据.词条[ct], 100) + "%<br>");
                            }
                        }
                        if (hqList[i].数据.待保留词条 != null) {
                            $("._tishi").show();


                            var nextCT = new Array();
                            for (var ct in hqList[i].数据.待保留词条) {
                                nextCT.push(ct + "：+" + accMul(hqList[i].数据.待保留词条[ct], 100) + "%<br>");
                            }

                            $(".tct").html(thisCT.concat());
                            $(".nct").html(nextCT.concat());
                        }
                        $(".ct_info1").html(thisCT.concat());
                        $(".hqlh_img").css("background-image", "url(images/hunqi/" + hqList[i].图标 + ");");
                        $(".jlv").html("lv." + hqList[i].数据.元素等级.金);
                        $(".jin_xg").html(hqList[i].数据.金效果 + "：" + accMul(hqList[i].数据.金属性, 100) + "%")
                        countSX[hqList[i].数据.金效果] = accAdd(countSX[hqList[i].数据.金效果], hqList[i].数据.金属性);

                        $(".mlv").html("lv." + hqList[i].数据.元素等级.木);
                        $(".mu_xg").html(hqList[i].数据.木效果 + "：" + accMul(hqList[i].数据.木属性, 100) + "%")
                        countSX[hqList[i].数据.木效果] = accAdd(countSX[hqList[i].数据.木效果], hqList[i].数据.木属性);

                        $(".slv").html("lv." + hqList[i].数据.元素等级.水);
                        $(".shui_xg").html(hqList[i].数据.水效果 + "：" + accMul(hqList[i].数据.水属性, 100) + "%")
                        countSX[hqList[i].数据.水效果] = accAdd(countSX[hqList[i].数据.水效果], hqList[i].数据.水属性);

                        $(".hlv").html("lv." + hqList[i].数据.元素等级.火);
                        $(".huo_xg").html(hqList[i].数据.火效果 + "：" + accMul(hqList[i].数据.火属性, 100) + "%")
                        countSX[hqList[i].数据.火效果] = accAdd(countSX[hqList[i].数据.火效果], hqList[i].数据.火属性);

                        $(".tlv").html("lv." + hqList[i].数据.元素等级.土);
                        $(".tu_xg").html(hqList[i].数据.土效果 + "：" + accMul(hqList[i].数据.土属性, 100) + "%")
                        countSX[hqList[i].数据.土效果] = accAdd(countSX[hqList[i].数据.土效果], hqList[i].数据.土属性);

                        $(".alv").html("lv." + hqList[i].数据.元素等级.暗);
                        $(".an_xg").html(hqList[i].数据.暗效果 + "：" + accMul(hqList[i].数据.暗属性, 100) + "%")
                        countSX[hqList[i].数据.暗效果] = accAdd(countSX[hqList[i].数据.暗效果], hqList[i].数据.暗属性);
                        var countSX_ARR = new Array();
                        for (var c in countSX) {

                            if (countSX[c] > 0) {

                                countSX_ARR.push(c + "：+" + accMul(countSX[c], 100) + "%<br>");
                            }
                        }

                        $(".count_sx").html(countSX_ARR.concat(""));
                        $(".hqpj").html(hq_pj[hqList[i].数据.转灵品级]);
                        $(".hqlv").html("lv." + hqList[i].数据.等级);
                        if (hqList[i].强化设定.最高等级 > hqList[i].数据.等级) {
                            hq_qhInfo = "所需材料：" + hqList[i].强化设定.所需道具 + "*" + hqList[i].强化设定.所需数量[hqList[i].数据.等级 + 1] +
                                "<br>最高等级：" + hqList[i].强化设定.最高等级;
                        } else {
                            hq_qhInfo = "当前已经满级，无法继续强化！";
                        }
                        hq_xlInfo = "所需材料：" + hqList[i].洗练设定.所需道具 + "*1" +
                            "、时之结晶*" + hqList[i].洗练设定.所需结晶[hqList[i].数据.等级];
                        hq_zlInfo = "所需材料：" + hqList[i].转灵设定.所需道具 + "*" + hqList[i].转灵设定.所需数量[hqList[i].数据.转灵品级] +
                            "、时之结晶*" + hqList[i].转灵设定.所需结晶[hqList[i].数据.转灵品级];

                    }
                }

            }

        }
        var list = window.parent.sjson;
        var sbMax = 0;
        var thisSBindex = 0;

        function loadSb(JSON) {
            JSON = $.parseJSON(JSON);
            sbMax = 0;
            for (var key in JSON) {
                for (var j = 0; j < list.length; j++) {
                    if (list[j].兵魂.名字 == key) {
                        list[j].兵魂.获得状态 = true;
                        list[j].兵魂.等级 = JSON[key];
                        list[j].获得状态 = true;
                    }
                    if (list[j].神兵.名字 == key) {
                        list[j].神兵.获得状态 = true;
                        list[j].神兵.等级 = JSON[key];
                        list[j].获得状态 = true;
                    }
                    if (list[j].巫灵.名字 == key) {
                        list[j].巫灵.获得状态 = true;
                        list[j].巫灵.等级 = JSON[key];
                        list[j].获得状态 = true;
                    }
                }
            }

            for (var i = 0; i < list.length; i++) {
                if (list[i].获得状态 == true) {
                    sbMax++;
                }
            }
            $(".sb_page_max").html(sbMax);
            $(".sb_page_this").html(thisSBindex);
            if (sbMax <= 0) {
                $(".sb_box_1").hide();
                $(".sb_box_2").hide();
                $(".sb_next").hide();
                $(".sb_box_2").hide();
                return;
            }
            loadSBINFO(thisSBindex);
        }
        var pjson = window.parent.pjson;
        var pfMax = 0;
        var thisPFindex = 0;

        function loadPf(JSON) {
            JSON = $.parseJSON(JSON);
            var count = 0;
            for (var i in JSON) {
                var pname = JSON[i];
                for (var j = 0; j < pjson.length; j++) {

                    if (pjson[j].皮肤名 == pname) pjson[j].获得状态 = true;

                }
                count++;
            }
            pfMax = Math.ceil(count / 5);
            $(".pf_page_max").html(pfMax);
            $(".pf_page_this").html(thisPFindex);

            loadPFINFO(thisPFindex);
        }

        function loadPFINFO(index) {
            if (index + 1 > pfMax) {
                window.parent.alert("已经是最后一页了！");
                return;
            }
            if (index < 0) {
                window.parent.alert("已经是第一页了！");
                return;
            }
            thisPFindex = index;
            $(".pf_page_this").html(thisPFindex + 1);
            var i = 0;
            var min = (index) * 5;
            //1 1 5
            //2 5 10
            $(".petCard_list").html("");
            var html =
                '<div class="petCard" data-id="{id}">' +
                '<div class="pcInfoBox">{info}' +
                '</div>' +
                '<div class="pcInfo" style="background-image: url(images/pifuwu/{xx}.png);"></div>' +
                '</div>';
            var max = min + 5;
            for (var j = 0; j < pjson.length; j++) {
                if (pjson[j].获得状态 == true) {
                    i++;
                    if (i > min && i <= max) {

                        var info = "";
                        for (var sx in pjson[j].属性) {
                            if (info != "") info += "<br>";
                            info += sx + "：+" + accMul(pjson[j].属性[sx], 100).replace(".00", "") + "%<br>";

                        }
                        var inh = html.replace("{xx}", pjson[j].形象ID)
                            .replace("{id}", pjson[j].皮肤名)
                            .replace("{id}", pjson[j].皮肤名)
                            .replace("{info}", info);
                        $(".petCard_list").append(inh)
                    }

                }
            }
            $(".petCard").mousemove(function() {
                $(".pcInfoBox").hide();
                $(this).find(".pcInfoBox").show();
            })
            $(".petCard").mouseout(function() {
                $(this).find(".pcInfoBox").hide();
            })
            $(".petCard").click(function() {
                window.parent.alert(window.external.pf_pd($(this).attr("data-id")));
            })

        }

        function upSb(id) {
            var r = window.external.SKSD_SB_QH(id);
            if (r.indexOf("成功") != -1) window.external.updateSKSD_Page();
            window.parent.alert(r);

        }

        function loadSBINFO(index) {
            if (index + 1 > sbMax) {
                window.parent.alert("已经是最后一页了！");
                return;
            }
            if (index < 0) {
                window.parent.alert("已经是第一页了！");
                return;
            }
            thisSBindex = index;
            $(".sb_page_this").html(thisSBindex + 1);
            for (var i = 0; i < list.length; i++) {
                if (list[i].获得状态 == true) {
                    if (i == index) {
                        $(".noSB").hide();
                        $(".bhinfo").html("");
                        $(".bhinfo1").html("");
                        $(".sbinfo").html("");
                        $(".sbinfo1").html("");
                        $(".wlinfo").html("");
                        $(".wlinfo1").html("");
                        $(".sb_img_wl,.sb_img_bh,.sb_img_sb").css("background-image", "");
                        $(".sbname,.wlname,.bhname").html("");
                        if (list[i].兵魂.获得状态) {
                            $(".bhname").html(list[i].兵魂.名字);
                            var info = list[i].兵魂.属性[list[i].兵魂.等级 + ''];
                            var str = "<div style='position:absolute;margin-top:70px;margin-left:15px'>LV." + list[i].兵魂.等级 + "</div>";
                            for (var k in info) {
                                str += k + "：" + accMul(info[k], 100) + "%<br>";
                            }
                            var str1 = "<div style='position:absolute;margin-top:70px;margin-left:36px'>LV." + list[i].兵魂.等级 + "</div>";
                            str1 += list[i].兵魂.强化设定.所需道具 +
                                "*" +
                                list[i].兵魂.强化设定.所需数量[[list[i].兵魂.等级 + 1]];
                            if (str1.indexOf("undefined") != -1) str1 = "已满级";
                            $(".bhinfo1").html(str1);
                            $(".bhinfo").attr("data-id", list[i].兵魂.名字);
                            $(".bhinfo").html(str);
                            $(".sb_img_bh").css("background-image", "url(images/shenbing/" + list[i].兵魂.图片素材 + ");");
                        }
                        if (list[i].神兵.获得状态) {
                            $(".sbname").html(list[i].神兵.名字);
                            var info = list[i].神兵.属性[list[i].神兵.等级];
                            var str = "<div style='position:absolute;margin-top:60px;margin-left:65px'>LV." + list[i].神兵.等级 + "</div>";
                            for (var k in info) {
                                str += k + "：" + accMul(info[k], 100) + "%<br>";
                            }
                            $(".sbinfo").html(str);
                            $(".sbinfo").attr("data-id", list[i].神兵.名字);
                            var str1 = "<div style='position:absolute;margin-top:60px;margin-left:65px'>LV." + list[i].神兵.等级 + "</div>";
                            str1 += list[i].神兵.强化设定.所需道具 +
                                "*" +
                                list[i].神兵.强化设定.所需数量[[list[i].神兵.等级 + 1]];
                            if (str1.indexOf("undefined") != -1) str1 = "已满级";
                            $(".sbinfo1").html(str1);
                            $(".sb_img_sb").css("background-image", "url(images/shenbing/" + list[i].神兵.图片素材 + ");");
                        }
                        if (list[i].巫灵.获得状态) {
                            $(".wlname").html(list[i].巫灵.名字);
                            var info = list[i].巫灵.属性[list[i].巫灵.等级];
                            var str = "<div style='position:absolute;margin-top:70px;margin-left:15px'>LV." + list[i].巫灵.等级 + "</div>";
                            for (var k in info) {
                                str += k + "：" + accMul(info[k], 100) + "%<br>";
                            }
                            $(".wlinfo").html(str);
                            $(".wlinfo").attr("data-id", list[i].巫灵.名字);
                            var str1 = "<div style='position:absolute;margin-top:70px;margin-left:36px'>LV." + list[i].巫灵.等级 + "</div>";
                            str1 += list[i].巫灵.强化设定.所需道具 +
                                "*" +
                                list[i].巫灵.强化设定.所需数量[[list[i].巫灵.等级 + 1]];
                            if (str1.indexOf("undefined") != -1) str1 = "已满级";
                            $(".wlinfo1").html(str1);
                            $(".sb_img_wl").css("background-image", "url(images/shenbing/" + list[i].巫灵.图片素材 + ");");
                        }
                    }
                }
            }
        }
        var thisHQID = -1;

        function hunqi_ct_xz(ok) {
            var r = window.external.SKSD_HQ_SAVE_CT(thisHQID, ok);
            if (r.indexOf("成功") != -1) {
                window.external.updateSKSD_HQ_Page();
                window.parent.recvMsg("sm|" + r);
            } else window.parent.alert(r);
            $("._tishi").hide();
        }

        function hunqi_up() {
            var r = window.external.SKSD_HQ_QH(thisHQID);
            if (r.indexOf("成功") != -1) window.external.updateSKSD_HQ_Page();
            window.parent.alert(r);
        }

        function hunqi_zhuanling() {
            var r = window.external.SKSD_HQ_ZL(thisHQID);
            if (r.indexOf("成功") != -1) {
                window.external.updateSKSD_HQ_Page();
                window.parent.recvMsg("sm|" + r);
            } else window.parent.alert(r);
        }

        function hunqi_xilian() {
            var r = window.external.SKSD_HQ_XL(thisHQID);
            if (r.indexOf("成功") != -1) window.external.updateSKSD_HQ_Page();
            else window.parent.alert(r);
        }

        function hunqi_up_ys(ys) {
            var r = window.external.SKSD_HQ_UP_YS(thisHQID, ys);
            if (r.indexOf("成功") != -1) window.external.updateSKSD_HQ_Page();
            window.parent.alert(r);
        }
        $(function() {
            $(".hqlh_img").hide();
            $(".hqlh_icoList").hide();
            $(".hqlh_btnlist").hide();
            $(".sb_pageInfo").hide();
            $(".hq_page_name").hide();
            $(".hq_next").hide();
            document.body.onselectstart = document.body.ondrag = function() {
                return false;
            }
            $(".ico_img").mousemove(function() {
                $(".hei35").hide();
                $(this).find(".hei35").show();
            })
            $(".ico_img").mouseout(function() {
                $(this).find(".hei35").hide();

            })

            $(".hqlh_img").mousemove(function() {
                if ($(".ct_info").is(":visible")) {
                    return;
                }
                $(".hq_info").hide();
                $(this).find(".count_info").show();
            })
            $(".hqlh_img").mouseout(function() {
                if ($(".ct_info").is(":visible")) {
                    return;
                }
                $(this).find(".hq_info").hide();
            })
            $(".btnys").mousemove(function() {
                $(".hq_info").hide();
                $(".ys_info").show();
            })
            $(".btnys").mouseout(function() {
                $(".hq_info").hide();
            })
            $(".btn_hq").mousemove(function() {
                $(".hq_info").hide();
                $(".ct_info").show();
            })
            $(".btn_hq").mouseout(function() {
                $(".hq_info").hide();
            })

            $(".sb_img_2,.sb_img").mousemove(function() {
                if ($(this).find("div:eq(0)").html() == "") return;
                $(".bhInfoBox").hide();
                $(this).find(".bhInfoBox").show();
            })
            $(".sb_img_2,.sb_img").mouseout(function() {
                $(this).find(".bhInfoBox").hide();
            })

            $(".btn_hqqh").mousemove(function() {
                $(".tip").show().removeClass().
                addClass("tip").addClass("tip_qianghua")
                    .html(hq_qhInfo);
                    $(".tip").css("top", e.pageY - 90);
            })
            $(".btn_hqqh").mouseout(function() {
                $(".tip").hide();
            })

            $(".btn_hqzl").mousemove(function() {
                $(".tip").show().removeClass().
                addClass("tip").addClass("tip_zhuanling")
                    .html(hq_zlInfo);
            })
            $(".btn_hqzl").mouseout(function() {
                $(".tip").hide();
            })

            $(".btn_hqxl").mousemove(function() {
                $(".tip").show().removeClass().
                addClass("tip").addClass("tip_xilian")
                    .html(hq_xlInfo);
            })
            $(".btn_hqxl").mouseout(function() {
                $(".tip").hide();
            })
            $(".btnys").mousemove(function(e) {
                var yname = $(this).attr("data-ys");
                var ys = thisHQ.元素设定.属性规则[yname];
                var num = thisHQ.元素设定.默认规则[ys.强化设定.指定数量配置][thisHQ.数据.元素等级[yname] + 1];
                var propinfo = "";
                if (ys.强化设定.满级 > thisHQ.数据.元素等级[yname]) {
                    propinfo = ys.强化设定.道具 + "*" + num;

                } else {
                    propinfo = "已经满级"
                }
                $(".tip").show().removeClass().
                addClass("tip").addClass("tip_ys")
                    .html("<b STYLE='FLOAT:LEFT;margin-left:10px;'>强化所需：</b><span style='float:LEFT'>" + propinfo +
                        "</span><br><b STYLE='FLOAT:LEFT;margin-left:10px;'>当前属性：</b><span style='float:LEFT'>" +
                        thisHQ.数据[yname + "效果"] + accMul(thisHQ.数据[yname + "属性"], 100) + "%<span>");
                $(".tip").css("top", e.pageY - 90);
            })
            $(".btnys").mouseout(function() {
                $(".tip").hide();

            })
            window.external.updateSKSD_Page();
        })

        function accDiv(arg1, arg2) {
            var t1 = 0,
                t2 = 0,
                r1, r2;
            try {
                t1 = arg1.toString().split(".")[1].length
            } catch (e) {}
            try {
                t2 = arg2.toString().split(".")[1].length
            } catch (e) {}
            with(Math) {
                r1 = Number(arg1.toString().replace(".", ""))
                r2 = Number(arg2.toString().replace(".", ""))
                return accMul((r1 / r2), pow(10, t2 - t1));
            }
        }
        //乘法 
        function accMul(arg1, arg2) {
            if (arg1 == null) arg1 = 0;
            if (arg2 == null) arg1 = 0;
            var m = 0,
                s1 = arg1.toString(),
                s2 = arg2.toString();
            try {
                m += s1.split(".")[1].length
            } catch (e) {}
            try {
                m += s2.split(".")[1].length
            } catch (e) {}
            return (Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m)).toFixed(2)
        }
        //加法 
        function accAdd(arg1, arg2) {
            var r1, r2, m;
            try {
                r1 = arg1.toString().split(".")[1].length
            } catch (e) {
                r1 = 0
            }
            try {
                r2 = arg2.toString().split(".")[1].length
            } catch (e) {
                r2 = 0
            }
            m = Math.pow(10, Math.max(r1, r2))
            return (arg1 * m + arg2 * m) / m
        }
        //减法 
        function Subtr(arg1, arg2) {
            var r1, r2, m, n;
            try {
                r1 = arg1.toString().split(".")[1].length
            } catch (e) {
                r1 = 0
            }
            try {
                r2 = arg2.toString().split(".")[1].length
            } catch (e) {
                r2 = 0
            }
            m = Math.pow(10, Math.max(r1, r2));
            n = (r1 >= r2) ? r1 : r2;
            return ((arg1 * m - arg2 * m) / m).toFixed(n);
        }
    </script>
</head>

<body>
    <div class="main_box">

        <div class="left">

        </div>
        <div class="right">
            <div class="topMenu" style="position: relative; z-index: 1000;">
                <ul class="task_nav">
                    <li onclick="setTab(1,this)" class="on"><a class="a01" href="javascript:void(0)">神兵阁</a></li>
                    <li onclick="setTab(2,this)"><a class="a02" href="javascript:void(0)">皮肤屋</a></li>
                    <li onclick="setTab(3,this)"><a class="a03" href="javascript:void(0)">魂器炼化</a></li>
                    <li onclick="setTab(4,this)"><a>远古神龙</a></li>
                    <li onclick="setTab(5,this)"><a>侍灵召唤</a></li>
                    <li onclick="setTab(6,this)" style="display: none;"><a class="a06" href="javascript:void(0)">时空画卷</a></li>
                    <li onclick="setTab(7,this)"><a>成长突破</a></li>
                </ul>
            </div>
            <div class="box">
                <div class="tabbox tab_1">
                    <div class="noSB" style="color: #000;font-weight:bold;position: absolute;margin-top: 108px;margin-left: 265px; font-size: 20px;">当前无神兵</div>
                    <div class="sb_box_1">

                        <div class="title_bh">

                        </div>
                        <div class="sb_title bhname">

                        </div>
                        <div class="sb_img sb_img_bh">
                            <div class="bhInfoBox bhinfo" data-id='0'>
                                攻击：30%<br> 生命：30%
                                <br> 防御：30%

                            </div>
                            <div class="bhInfoBox1 bhinfo1"></div>
                        </div>
                        <div class="sb_btn1" title="暂未开发">

                        </div>
                        <div class="sb_btn2" title="强化" onmousemove="if($('.bhinfo1').html()=='')return;$('.bhinfo1').show()" onmouseout="$('.bhinfo1').hide()" onclick="upSb($('.bhinfo').attr('data-id'))">

                        </div>
                    </div>
                    <div class="sb_box_2">
                        <div class="title_sb">

                        </div>
                        <div class="sb_title sbname">

                        </div>
                        <div class="sb_img_2 sb_img_sb">
                            <div class="bhInfoBox sbinfo" data-id='0'>
                                攻击：30%<br> 生命：30%
                                <br> 防御：30%

                            </div>
                            <div class="bhInfoBox1 sbinfo1"></div>
                        </div>
                        <div class="sb_btn1" title="暂未开发">

                        </div>
                        <div class="sb_btn2" onmousemove="if($('.sbinfo1').html()=='')return;$('.sbinfo1').show()" onmouseout="$('.sbinfo1').hide()" title="强化" onclick="upSb($('.sbinfo').attr('data-id'))">

                        </div>
                    </div>
                    <div class="sb_box_1">
                        <div class="title_wl">

                        </div>
                        <div class="sb_title wlname">
                            巫灵名字
                        </div>
                        <div class="sb_img sb_img_wl" data-id='0'>
                            <div class="bhInfoBox wlinfo">
                                攻击：30%<br> 生命：30%
                                <br> 防御：30%

                            </div>
                            <div class="bhInfoBox1 wlinfo1"></div>
                        </div>
                        <div class="sb_btn1" title="暂未开发">

                        </div>
                        <div class="sb_btn2" onmousemove="if($('.wlinfo1').html()=='')return;$('.wlinfo1').show()" onmouseout="$('.wlinfo1').hide()" title="强化" onclick="upSb($('.wlinfo').attr('data-id'))">


                        </div>
                    </div>
                    <div class="sb_next">
                        <img class="sb_left" onclick="loadSBINFO(thisSBindex-1)" title="上一页" src="images/sb_left.png">
                        <img class="sb_right" onclick="loadSBINFO(thisSBindex+1)" title="下一页1" src="images/sb_right.png">
                    </div>
                    <div title="页码/最大页" class="sb_pageInfo">
                        [<span class="sb_page_this">1</span>/<span class="sb_page_max">10</span>]
                    </div>
                </div>
                <div class="tabbox tab_2" style="display: none;">
                    <div class="petCard_list">


                    </div>
                    <div style="clear: both;"></div>
                    <div class="pf_next">
                        <img class="pf_left" onclick="loadPFINFO(thisPFindex-1)" title="上一页" src="images/sb_left.png">
                        <img class="pf_right" onclick="loadPFINFO(thisPFindex+1)" title="下一页" src="images/sb_right.png">
                      
                    </div>
                    <div title="页码/最大页" class="sb_pageInfo">
                        [<span class="pf_page_this">1</span>/<span class="pf_page_max">null</span>]<br>
                        <span style="cursor: pointer;" href="javascript:void(0);" onclick="window.parent.alert(window.external.pf_xxALL('0'))">卸皮肤</span>
                    </div>
                </div>
                <div class="tabbox tab_3" style="display: none;">
                    <div class="noHQ" style="color: #fff;position: absolute;margin-top: 108px;margin-left: 265px; font-size: 20px;">当前无魂器</div>
                    <div class="tip">
                        <span>需要材料：</span>
                        <span>地狱碎片*100、时之结晶*5000</span>
                    </div>
                    <div class="_tishi" style="display: none;">
                        <div class="_tishi_text">

                            <div class="this_ct _ct">
                                <div class="tishi" style="color: #390902;">当前词条：</div>
                                <div class="tct">
                                    命中：130%<br> 攻击：130%
                                    <br> 生命：130%
                                    <br> 防御：130%
                                    <br> 速度：310%
                                    <br> 加深：130%
                                    <br> 吸血：130%
                                    <br> 抵消：130%
                                    <br> 吸魔：130%
                                </div>
                            </div>
                            <div class="_jt">→</div>

                            <div class="next_ct _ct" style="color: #9b5a3d;font-style: italic;">
                                <div class="tishi">新词条：</div>
                                <div class="nct">
                                    命中：130%<br> 攻击：130%
                                    <br> 生命：130%
                                    <br> 防御：130%
                                    <br> 速度：310%
                                    <br> 加深：130%
                                    <br> 吸血：130%
                                    <br> 抵消：130%
                                    <br> 吸魔：130%
                                </div>

                            </div>
                        </div>
                        <div class="_tishi_bottom" onclick="hunqi_ct_xz(true)">
                            保留
                        </div>
                        <div class="_tishi_bottom fq" onclick="hunqi_ct_xz(false)" style="margin-top: 136px;">
                            放弃
                        </div>
                    </div>
                    <div class="hqlh_btnlist">
                        <div class="ico_img btn_hqqh btn_hq" style="background-image: url(images/btn_qianghua.png);margin-top: 50px;">
                            <div class="hei35" onclick="hunqi_up()">
                                强化
                            </div>
                        </div>
                        <div class="ico_img" style="background-image: url(images/btn_zhujin.png);">
                            <div class="hei35 btn_hqzl btn_hq" onclick="hunqi_zhuanling()">
                                转灵
                            </div>
                        </div>
                        <div class="ico_img" style="background-image: url(images/btn_xilian.png);" onclick="hunqi_xilian()">
                            <div class="hei35 btn_hqxl btn_hq">
                                洗练
                            </div>
                        </div>
                    </div>

                    <div class="hqlh_img" style="background-image: url(images/hunqi/hq1.png);">
                        <div class="hq_info ys_info">
                            <div style="margin-top: 25px;font-size:12px">
                                [元素效果]<span class="ck" onclick="$('.hq_info').hide();$('.ct_info').show();">查看词条</span>
                            </div>

                            <div class="hq_info_sz">
                                <div><span>[金]</span><span class="jin_xg">命中：30%</span></div>
                                <div><span>[火]</span><span class="huo_xg">命中：30%</span></div>
                                <div><span>[木]</span><span class="mu_xg">命中：30%</span></div>
                                <div><span>[土]</span><span class="tu_xg">命中：30%</span></div>
                                <div><span>[水]</span><span class="shui_xg">命中：30%</span></div>
                                <div><span>[暗]</span><span class="an_xg">命中：30%</span></div>
                            </div>

                        </div>
                        <div class="hq_info count_info">
                            <div style="margin-top: 25px;font-size:12px">
                                [全部属性]<span class="ck" onclick="$('.hq_info').hide();$('.ct_info').show();">查看词条</span>
                            </div>
                            <div class="hq_info_jc">
                                <div class="_min hqlv">LV.1</div>

                            </div>
                            <div class="hq_info_jc1">
                                <div class="_min _zi hqpj">优秀</div>
                            </div>
                            <div class="hq_info_sz count_sx">
                                <div>命中：130%</div>
                                <div>攻击：130%</div>
                                <div>生命：130%</div>
                                <div>防御：130%</div>
                                <div>速度：310%</div>
                                <div>加深：130%</div>
                                <div>吸血：130%</div>
                                <div>抵消：130%</div>
                                <div>吸魔：130%</div>
                            </div>

                        </div>
                        <div class="hq_info ct_info">
                            <div style="margin-top: 25px;font-size:12px">
                                [词条属性]<span class="ck" onclick="$('.hq_info').hide();$('.count_info').show();">查看全部</span>
                            </div>
                            <div class="hq_info_jc">
                                <div class="_min hqlv">LV.1</div>

                            </div>
                            <div class="hq_info_jc1">
                                <div class="_min _zi hqpj">优秀</div>
                            </div>
                            <div class="hq_info_sz ct_info1">
                                <div>命中：130%</div>
                                <div>攻击：130%</div>
                                <div>生命：130%</div>
                            </div>

                        </div>
                    </div>
                    <div class="hqlh_icoList">
                        <div class="ico_row" style="margin-top: 54px;">
                            <div class="ico_img btnys" data-ys="金" onclick="hunqi_up_ys('金')" style="background-image: url(images/ico_jin.png);">

                                <div class="hei35 jlv">
                                    lv30
                                </div>
                            </div>
                            <div class="ico_img btnys" data-ys="火" onclick="hunqi_up_ys('火')" style="background-image: url(images/ico_huo.png);">
                                <div class="hei35 hlv">
                                    lv30
                                </div>
                            </div>
                        </div>
                        <div class="ico_row">
                            <div class="ico_img btnys" data-ys="木" onclick="hunqi_up_ys('木')" style="background-image: url(images/ico_mu.png);">

                                <div class="hei35 mlv">
                                    lv30
                                </div>
                            </div>
                            <div class="ico_img btnys" data-ys="土" onclick="hunqi_up_ys('土')" style="background-image: url(images/ico_tu.png);">
                                <div class="hei35 tlv">
                                    lv30
                                </div>
                            </div>
                        </div>
                        <div class="ico_row">
                            <div class="ico_img btnys" data-ys="水" onclick="hunqi_up_ys('水')" style="background-image: url(images/ico_shui.png);">

                                <div class="hei35 slv">
                                    lv30
                                </div>
                            </div>
                            <div class="ico_img btnys" data-ys="暗" onclick="hunqi_up_ys('暗')" style="background-image: url(images/ico_an.png);">
                                <div class="hei35 alv">
                                    lv30
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="hq_next">
                        <!--<img class="hq_left" onclick="loadHQINFO(thisHQindex-1)" title="上一页" src="images/sb_left.png">
                        <img class="hq_right" onclick="loadHQINFO(thisHQindex+1)" title="下一页" src="images/sb_right.png">-->

                        <img class="hq_left" onclick="loadHQINFO(thisHQindex-1)" title="上一页" src="images/sb_left.png">
                        <img class="hq_right1" onclick="loadHQINFO(thisHQindex+1)" title="下一页" src="images/sb_right.png">
                    </div>
                    <div title="页码/最大页" class="sb_pageInfo">
                        [<span class="hq_page_this">1</span>/<span class="hq_page_max">10</span>]

                    </div>
                    <div style="float: right;margin-top: -291px;color: #FFF;font-size: 12px;margin-right: 5px;" class="hq_page_name">大宝剑·测试2</div>
                </div>

                <!-- 成长突破选项卡 -->
                <div class="tabbox tab_7" style="display: none;">
                    <div class="tupo-bg" style="position: relative;">
                        <!-- 魔法阵背景 -->
                        <div class="tupo-magic"></div>
                        <!-- LvXXX 装饰 -->
                        <div class="tupo-lv" id="magicCircleLevel">Lv</div>

                        <!-- 能量汇聚弹框 -->
                        <div id="nenglianghuiju" class="energy-popup" style="display: none; z-index: 105;">
                            <div class="energy-popup-bg"></div>
                            <div class="energy-popup-close" onclick="hidePopup()"></div>
                            <div class="energy-popup-content">
                                <div class="energy-row" style="margin-top: 30px;">
                                    <div>
                                        <span class="energy-label" style="display: inline-block; position: relative; left: 35px;">*使用数量</span>
                                    </div>
                                    <div>
                                        <img src="images/chengzhangtup/能量汇聚素材/聚灵晶石.png" width="28" height="28" style="vertical-align:middle; margin-right: 5px;">
                                        <input class="energy-input" id="julingStoneInput" type="text" placeholder="输入数量">
                                        <span class="energy-use-item-btn" onclick="useJulingStone()">使用道具</span>
                                    </div>
                                </div>
                                <div class="energy-row energy-level" id="currentLevel" style="margin-left:33px;font-size: 14px;margin-top: 30px; font-weight: 600;">当前等级：Lv0</div>
                                <div class="energy-row" style="margin-left:33px;font-size: 14px; font-weight: 600;">升级所需经验</div>
                                <div class="energy-row energy-exp" id="needExp" style="margin-left:33px;color:#c00;font-weight:bold;font-size: 14px; font-weight: 600;"></div>
                            </div>
                        </div>

                        <!-- 成长突破弹框 -->
                        <div id="chengzhangtupo" class="energy-popup" style="display: none; z-index: 105;">
                            <div class="energy-popup-bg"></div>
                            <div class="energy-popup-close" onclick="hidePopup()"></div>
                            <div class="energy-popup-content">
                                <div id="breakthroughLevel" style="position: relative;left: 31px;font-size: 13px;font-weight: 600;">当前突破阶级：Lv0</div>
                                <div class="energy-row" style="margin-top: 10px;">
                                    <div>
                                        <span class="energy-label" style="
                                            display: inline-block;
                                            position: relative;
                                            left: 35px;
                                        ">*使用数量</span>
                                    </div>
                                    <div>
                                        <img src="images/chengzhangtup/chenzhangtupo/突破圣石.png" width="28" height="28" style="vertical-align:middle; margin-left: 5px;">
                                        <span>突破圣石自动扣除</span>
                                    </div>
                                </div>
                                <div class="" style="margin-top: 5px;">
                                    <div>
                                        <span class="energy-label" style="
                                            display: inline-block;
                                            position: relative;
                                            left: 35px;
                                        ">*使用数量</span>
                                    </div>
                                    <div>
                                        <img src="images/chengzhangtup/chenzhangtupo/凤凰晶石.png" width="28" height="28" style="vertical-align:middle; margin-left: 5px;">
                                        <input class="energy-input" id="phoenixStoneCount" type="text">
                                        <span class="energy-use-item-btn" onclick="usePhoenixStone()">使用道具</span>
                                    </div>
                                </div>
                                <div class="energy-level" style="border: 0px solid #fff;display: inline-block;width: 104px;height: 50px;margin-top: 5px;">
                                    <div style="margin-top: 5px;">
                                        <img src="images/chengzhangtup/chenzhangtupo/凤凰晶石.png" width="28" height="28" style="vertical-align:middle;margin-right: 5px;width: 22px;height: 22px;margin-left: 5px;">
                                        <span id="phoenixStoneStatus" style="font-weight: 700;">0/0</span>
                                    </div>
                                </div>
                                <div class="energy-row" style="border: 0px solid #fff;display: ruby;width: 50px;height: 50px;margin-right: 5px;">
                                    <span class="energy-use-chengzhangtupo-btn" onclick="executeBreakthrough()">成长突破</span>
                                </div>
                                <div id="breakthroughSuccessRate" style="margin-top: 0px;width: 165px;height: 20px;border-radius: 5px;text-align: center;line-height: 20px;font-weight: 700;color: red;">
                                    当前成功率：0%
                                </div>
                            </div>
                        </div>

                        <!-- 横向按钮组 -->
                        <div class="tupo-btn-group">
                            <div class="tupo-btn" onclick="showPopup('nenglianghuiju')">
                                <img src="images/chengzhangtup/能量汇聚按钮.png" alt="能量汇聚" onmousedown="tupoBtnDown(this)" onmouseup="tupoBtnUp(this)" onmouseleave="tupoBtnUp(this)">
                            </div>
                            <div class="tupo-btn" onclick="showPopup('chengzhangtupo')">
                                <img src="images/chengzhangtup/成长突破按钮.png" alt="成长突破" onmousedown="tupoBtnDown(this)" onmouseup="tupoBtnUp(this)" onmouseleave="tupoBtnUp(this)">
                            </div>
                            <div class="tupo-btn">
                                <img src="images/chengzhangtup/种族突破按钮.png" alt="种族突破" onmousedown="tupoBtnDown(this)" onmouseup="tupoBtnUp(this)" onmouseleave="tupoBtnUp(this)">
                            </div>
                            <div class="tupo-btn">
                                <img src="images/chengzhangtup/境界突破按钮.png" alt="境界突破" onmousedown="tupoBtnDown(this)" onmouseup="tupoBtnUp(this)" onmouseleave="tupoBtnUp(this)">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 成长突破相关JavaScript代码
        $(document).ready(function() {
            // 重写showPopup函数以支持成长突破弹框
            var originalShowPopup = window.showPopup;
            window.showPopup = function (popupId) {
                originalShowPopup(popupId);
                if (popupId === 'nenglianghuiju') {
                    updateEnergyGatherInfo();
                } else if (popupId === 'chengzhangtupo') {
                    updateBreakthroughInfo();
                }
            };

            // 绑定突破按钮点击事件（备用方式）
            $('.tupo-btn-group img').eq(0).click(function() { showPopup('nenglianghuiju'); });
            $('.tupo-btn-group img').eq(1).click(function() { showPopup('chengzhangtupo'); });
            $('.tupo-btn-group img').eq(2).click(function() { showPopup('zhongzuetupo'); });
            $('.tupo-btn-group img').eq(3).click(function() { showPopup('jinjietupo'); });
            $('.energy-popup-close').click(function() { hidePopup(); });
        });

        // 成长突破相关变量
        var breakthroughInfo = null;
        var selectedPhoenixStones = 0;

        // 显示弹框
        function showPopup(popupId) {
            document.getElementById(popupId).style.display = 'block';
            if (popupId === 'nenglianghuiju') {
                updateEnergyGatherInfo();
            } else if (popupId === 'chengzhangtupo') {
                updateBreakthroughInfo();
            }
        }

        // 隐藏弹框
        function hidePopup() {
            var popups = document.querySelectorAll('.energy-popup');
            for (var i = 0; i < popups.length; i++) {
                popups[i].style.display = 'none';
            }
        }

        // 按钮效果
        function tupoBtnDown(btn) {
            btn.style.transform = 'scale(0.95)';
        }

        function tupoBtnUp(btn) {
            btn.style.transform = 'scale(1)';
        }

        // 使用聚灵晶石
        function useJulingStone() {
            var input = document.getElementById('julingStoneInput');
            var count = input ? input.value : prompt("请输入要使用的聚灵晶石数量：", "1");
            if (!count || isNaN(count) || parseInt(count) <= 0) {
                window.parent.alert("请输入正确的使用数量！");
                return;
            }

            try {
                var result = window.external.EnergyGather_UseJulingStone(count);
                window.parent.alert(result);

                // 刷新显示信息
                updateEnergyGatherInfo();
            } catch (e) {
                window.parent.alert("操作失败：" + e.message);
            }
        }

        // 更新能量汇聚信息显示
        function updateEnergyGatherInfo() {
            try {
                var infoJson = window.external.EnergyGather_GetInfo();
                var info = JSON.parse(infoJson);

                if (info.error) {
                    console.error("获取信息失败：" + info.error);
                    return;
                }

                // 更新显示元素（如果存在的话）
                if (document.getElementById('currentLevel')) {
                    document.getElementById('currentLevel').innerHTML = '当前等级：Lv' + info.currentLevel;
                }
                if (document.getElementById('needExp')) {
                    document.getElementById('needExp').innerHTML = info.currentExp + '/' + info.needExp;
                }
            } catch (e) {
                console.error("更新能量汇聚信息失败：", e);
            }
        }

        // 更新成长突破信息显示
        function updateBreakthroughInfo() {
            try {
                var infoJson = window.external.GrowthBreakthrough_GetInfo();

                if (infoJson.length <= 10) {
                    console.error("后端返回数据为空或过短：", infoJson);
                    window.parent.alert("无法获取突破信息，请检查后端接口！");
                    return;
                }

                breakthroughInfo = JSON.parse(infoJson);

                if (breakthroughInfo.error) {
                    console.error("获取突破信息失败：" + breakthroughInfo.error);
                    window.parent.alert("获取突破信息失败：" + breakthroughInfo.error);
                    return;
                }

                // 更新突破等级显示
                if (document.getElementById('breakthroughLevel')) {
                    document.getElementById('breakthroughLevel').innerHTML =
                        "当前突破阶级：Lv" + breakthroughInfo.currentLevel;
                }

                // 更新魔法阵中间的等级显示
                if (document.getElementById('magicCircleLevel')) {
                    document.getElementById('magicCircleLevel').innerHTML =
                        'Lv' + breakthroughInfo.currentLevel;
                }

                // 计算并显示总成功率（基础 + 累计，初始时凤凰晶石为0）
                if (document.getElementById('breakthroughSuccessRate')) {
                    var totalSuccessRate = breakthroughInfo.nextBreakthrough ?
                        Math.min(breakthroughInfo.nextBreakthrough.baseSuccessRate + breakthroughInfo.accumulatedSuccessRate, 100) : 0;
                    document.getElementById('breakthroughSuccessRate').innerHTML =
                        "当前成功率：" + totalSuccessRate + "%";
                }

                updatePhoenixStoneDisplay();

            } catch (e) {
                console.error("更新成长突破信息失败：", e);
                window.parent.alert("更新突破信息失败：" + e.message);
            }
        }

        // 更新凤凰晶石显示
        function updatePhoenixStoneDisplay() {
            if (document.getElementById('phoenixStoneStatus')) {
                document.getElementById('phoenixStoneStatus').textContent =
                    selectedPhoenixStones + "/3";
            }
        }

        // 使用凤凰晶石（选择要使用的数量）
        function usePhoenixStone() {
            var count = document.getElementById('phoenixStoneCount').value;
            if (!count || count <= 0) {
                window.parent.alert("请输入正确的使用数量！");
                return;
            }

            count = parseInt(count);
            if (count > 3) {
                window.parent.alert("单次突破最多使用3个凤凰晶石！");
                return;
            }

            selectedPhoenixStones = count;
            updatePhoenixStoneDisplay();
            window.parent.alert("已选择使用" + count + "个凤凰晶石！");
        }

        // 执行成长突破
        function executeBreakthrough() {
            if (!breakthroughInfo || !breakthroughInfo.nextBreakthrough) {
                window.parent.alert("无法执行突破！请先检查突破条件");
                return;
            }

            try {
                var result = window.external.GrowthBreakthrough_TryBreakthrough(selectedPhoenixStones);

                // 显示结果
                window.parent.alert(result);

                // 刷新显示
                updateBreakthroughInfo();

                // 重置凤凰晶石选择
                selectedPhoenixStones = 0;
                updatePhoenixStoneDisplay();

            } catch (e) {
                window.parent.alert("突破失败：" + e.message);
            }
        }

        // 使用突破圣石（检查道具数量）
        function useBreakthroughStone() {
            try {
                var infoJson = window.external.GrowthBreakthrough_GetInfo();
                var info = JSON.parse(infoJson);

                if (info.error) {
                    window.parent.alert("检查道具失败：" + info.error);
                    return;
                }

                window.parent.alert("突破圣石会在执行突破时自动消耗，无需手动使用！\n" +
                                  "当前突破等级：Lv" + info.currentLevel + "\n" +
                                  "神圣结界等级：Lv" + info.barrierLevel);
            } catch (e) {
                window.parent.alert("检查道具失败：" + e.message + "\n请确认道具ID是否正确配置！");
            }
        }
    </script>

</body>

</html>