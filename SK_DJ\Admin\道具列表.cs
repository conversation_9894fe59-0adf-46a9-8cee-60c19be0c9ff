﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Shikong.Pokemon2.PCG;

namespace Admin
{
    public partial class 道具列表 : Form
    {
        public 道具列表()
        {
            InitializeComponent();
        }

        List<PropType> 道具 = new DataProcess().ReadAllPropTypes();
        public string 类型 = "道具序号";
        public TextBox TEXT;
        private void 道具列表_Load(object sender, EventArgs e)
        {
       
            dataGridView1.DataSource = 道具;
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
           
           
        }

        private void dataGridView1_Click(object sender, EventArgs e)
        {
          
        }

        private void dataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
           
        }

        private void dataGridView1_CellContentDoubleClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void dataGridView1_CellMouseDoubleClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            try
            {
                if (
                    e.RowIndex > -1)
                {
                    foreach (DataGridViewRow r in dataGridView1.SelectedRows)
                    {
                        if (类型.Equals("道具序号"))
                        {
                            TEXT.Text += r.Cells[类型].Value.ToString();
                        }
                       
                    }
                    
                    Close();
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridView1.SelectedRows.Count > 0)
                {
                    string 序号 = "";
                    foreach (DataGridViewRow r in dataGridView1.SelectedRows)
                    {
                        if (类型.Equals("道具序号"))
                        {
                            序号 = 序号 + r.Cells[类型].Value.ToString() + "|";
                        }
                        else {
                            序号 = 序号 + r.Cells[类型].Value.ToString() + "、";
                        }
                    }
                    序号 = 序号 + "|";
                    序号 = 序号.Replace( "||","");
                    序号 = 序号.Replace("、|", "");
                    TEXT.Text +=  序号;
                    Close();
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            List<PropType> 新列表 = new List<PropType>();
            for (int i = 0; i < 道具.Count; i++)
            {
                if (道具[i].道具名字.IndexOf(textBox1.Text) != -1 || textBox1.Text.Length == 0)
                {
                    新列表.Add(道具[i]);
                }
            }
            dataGridView1.DataSource = 新列表;
        }
    }
}
