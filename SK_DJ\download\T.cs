﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static download.tools;

namespace download
{
    public static class T
    {
        private const string VKey = "HYFMWZS";
        private const string Key11 = "XGVUSZF";
        private const string Key12 = "LjIwMTYuMjAxNy4yMDE4LjIwMjAuMjAyMS4yMDIy";
        private const string Key21 = "JYSWQRT";
        private const string Key22 = "LjEyMzQuNTY3OC45MDEyLjM0NTYuNzg5MC5hYmNk";
        public static string GetKey(int keyIndex, bool dat = false)
        {
            if (keyIndex == 1)
            {
                if (dat)
                {
                    return SkCryptography.Vigenere.de(Key11, VKey).ToLower() + SkCryptography.Base64.DecodeBase64(Key12) + "ZNQMCK";

                }
                return SkCryptography.Vigenere.de(Key11, <PERSON><PERSON><PERSON>).ToLower() + SkCryptography.Base64.DecodeBase64(Key12);
            }

            if (keyIndex == 2)
            {
                return SkCryptography.Vigenere.de(Key21, V<PERSON><PERSON>).ToLower() + SkCryptography.Base64.DecodeBase64(Key22);
            }

            return null;
        }

    }
}
