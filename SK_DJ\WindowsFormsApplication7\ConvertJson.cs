﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;
using PetShikongTools;

namespace Shikong.Pokemon2.PCG
{
    public class ConvertJson
    {
        public string ListToJson(dynamic list)
        {
            return JsonConvert.SerializeObject(list);
            /*String JSON = "[";
            String[] l = 过滤字段.Split(',');

            if (List.Count >= 1)
            {
                dynamic e = List[0];
                Type t = e.GetType();
                PropertyInfo[] pList = t.GetProperties();

                for (int i = 0; i < List.Count; i++)
                {
                    Type t1 = List[i].GetType();
                    JSON += "{";
                    foreach (PropertyInfo p in pList)
                    {
                        bool tc = false;
                        foreach (String strr in l)
                        {
                            if (p.Name.Equals(strr))
                            {
                                tc = true;
                            }
                        }
                        if (tc)
                        {
                            continue;
                        }
                        if (t1.GetProperty(p.Name).GetValue(List[i]) != null)
                        {
                            dynamic value = t1.GetProperty(p.Name).GetValue(List[i]).ToString();
                            if (!zy)
                            {
                                JSON += "\"" + p.Name + "\"";
                                JSON += ":";
                                JSON += "\"" + value + "\"";
                            }
                            else {
                                JSON += "\\\"" + p.Name + "\\\"";
                                JSON += ":";
                                JSON += "\\\"" + value + "\\\"";
                            }
                            if (p != pList[pList.Length - 1])
                            {
                                JSON += ",";
                            }
                        }
                    }

                    JSON += "}";
                    if (i < List.Count - 1)
                    {
                        JSON += ",";
                    }
                }
                JSON += "]";
                while (JSON.IndexOf(",}") != -1)
                {
                    JSON = JSON.Replace(",}", "}");
                }
                return JSON;
            }
            else
            {
                return "Conver Error";
            }*/
        }

        internal string GetMallsJson(List<GoodsInfo> 道具)
        {  
            string json = "[";
            foreach (GoodsInfo 信息 in 道具)
            {
                json += "{";
                json += "\"商品序号\":\"" + 信息.商品序号 + "\",";
                json += "\"道具名字\":\"" + 信息.道具名字 + "\",";
                json += "\"道具图标\":\"" + 信息.道具图标 + "\",";
                json += "\"货币类型\":\"" + 信息.货币类型 + "\",";
                json += "\"商品价格\":\"" + (DataProcess.yrj_ && 信息.货币类型 =="时之结晶"? "1":信息.商品价格) + "\",";
                json += "\"商品数量\":\"" + 信息.商品数量 + "\"";
                json += "}";
                json += ",";
            }
            json += "]";

            json = json.Replace(",]", "]");

            return json;
        }

        internal string GetPropJson(List<PropInfo> 道具)
        {
            //数据处理.TmpPropType_List = new 数据处理().ReadAllPropTypes();

            StringBuilder json = new StringBuilder("[",32768);

            foreach (PropInfo 信息 in 道具)
            {
                json.Append("{");
                json.Append("\"道具类型ID\":\"" + 信息.道具类型ID + "\",");
                json.Append("\"道具数量\":\"" + 信息.道具数量 + "\",");
                if(信息.道具位置=="2") json.Append("\"道具位置\":\"" + 信息.道具位置 + "\",");
                json.Append("\"道具序号\":\"" + 信息.道具序号 + "\"");
                json.Append("}");

                json.Append(",");
            }

            json.Append("]");

            json = json.Replace(",]", "]");

            return json.ToString();

            /*var sw = new Stopwatch();

            sw.Start();


            string json = "[";
            foreach (PropInfo 信息 in 道具)
            {
                json += "{";
                json += "\"道具价格\":\"" + 信息.道具价格 + "\",";
                json += "\"道具类型ID\":\"" + 信息.道具类型ID + "\",";
                json += "\"道具名字\":\"" + 信息.道具名字 + "\",";
                json += "\"道具数量\":\"" + 信息.道具数量 + "\",";
                json += "\"道具图标\":\"" + 信息.道具图标 + "\",";
                json += "\"道具位置\":\"" + 信息.道具位置 + "\",";
                json += "\"道具序号\":\"" + 信息.道具序号 + "\"";
                json += "}";

                json += ",";

            }
            json += "]";

            /*while (json.IndexOf(",}", StringComparison.Ordinal) != -1)
            {
                json = json.Replace(",}", "}");
            }*/
            /*json = json.Replace(",]", "]");
            //数据处理.TmpPropType_List = null;

            sw.Stop();

            Console.WriteLine("time:" + sw.ElapsedMilliseconds); */
        }

        internal string GetPetJson(List<PetInfo> 宠物, bool 面板 = false)
        {


            StringBuilder json = new StringBuilder("[", 65536);

            foreach (PetInfo 信息 in 宠物)
            {
                json.Append("{");
                json.Append("\"宠物序号\":\"" + 信息.宠物序号 + "\",");
                json.Append("\"形象\":\"" + 信息.形象 + "\",");
                json.Append("\"当前经验\":\"" + 信息.当前经验 + "\",");
                if (信息.指定五行 != null)
                {
                    //这个if可能会造成写存档速度下降
                    json.Append("\"指定五行\":\"" + 信息.指定五行 + "\",");
                }
                json.Append("\"生命\":\"" + 信息.生命 + "\",");
                json.Append("\"魔法\":\"" + 信息.魔法 + "\",");
                json.Append("\"攻击\":\"" + 信息.攻击 + "\",");
                json.Append("\"防御\":\"" + 信息.防御 + "\",");
                json.Append("\"速度\":\"" + 信息.速度 + "\",");
                json.Append("\"状态\":\"" + 信息.状态 + "\",");
                json.Append("\"闪避\":\"" + 信息.闪避 + "\",");
                if (信息.自定义宠物名字 != null && 信息.自定义宠物名字 != "" && 信息.自定义宠物名字 != "0") { 
                    json.Append("\"自定义宠物名字\":\"" + 信息.自定义宠物名字 + "\","); 
                }
                json.Append("\"成长\":\"" + 信息.成长 + "\",");
                json.Append("\"命中\":\"" + 信息.命中 + "\",");
                json.Append("\"境界\":\"" + 信息.境界 + "\",");
                json.Append("\"技能列表\":\"" + 信息.技能列表 + "\",");
                if (面板)
                {
                    json.Append("\"技能显示\":\"" + 信息.技能显示 + "\",");

                }

                json.Append("\"已进化次数\":\"" + 信息.已进化次数 + "\"");
                json.Append("}");
                json.Append(",");
            }


            json.Append("]");

            json.Replace(",]", "]");

            return json.ToString();
            
        }
        internal bool 是否消耗双倍强化石(string id)
        {
            string[] list = {"巫" };
            return list.Contains(new DataProcess().获取装备类型信息(id).五行限制);
        }
        internal string GetZbJson(List<EquipmentInfo> 装备)
        {
            string json = "[";
            foreach (EquipmentInfo 信息 in 装备)
            {
                json += "{";
                json += "\"cID\":\"" + 信息.cID + "\",";
                json += "\"ICO\":\"" + 信息.ICO + "\",";
                json += "\"ID\":\"" + 信息.ID + "\",";
                json += "\"Name\":\"" + 信息.Name + "\",";
                json += "\"类ID\":\"" + 信息.类ID + "\",";
                json += "\"类型\":\"" + 信息.类型 + "\",";
                json += "\"强化\":\"" + 信息.强化 + "\",";
                /*if (信息.类型.Equals("灵饰"))
                {
                    json += "\"lssx\":\"" + 信息.LSSX + "\",";
                }*/
                json += "\"double\":\"" + 是否消耗双倍强化石(信息.类ID) + "\"";
                json += "}";

                json += ",";

            }
            json += "]";

            json = json.Replace(",]", "]");

            return json;
        }
        
        

        internal string GetUserJson(params string[] userInfo)
        {
            StringBuilder json = new StringBuilder("{",512);

            UserInfo user = new DataProcess().ReadUserInfo(); 

            foreach (string info in userInfo)
            {
                if (info.Equals("名字"))
                {
                    json.Append("\"名字\":\"" + user.名字 + "\",");
                }

                else if (info.Equals("sex"))
                {
                    json.Append("\"sex\":\"" + user.sex + "\",");
                }

                else if (info.Equals("论坛ID"))
                {
                    json.Append("\"论坛ID\":\"" + user.论坛ID + "\",");
                }

                else if (info.Equals("金币"))
                {
                    json.Append("\"金币\":\"" + user.金币 + "\",");
                }

                else if (info.Equals("元宝"))
                {
                    json.Append("\"元宝\":\"" + user.元宝 + "\",");
                }

                else if (info.Equals("水晶"))
                {
                    json.Append("\"水晶\":\"" + user.水晶 + "\",");
                }

                else if (info.Equals("威望"))
                {
                    json.Append("\"威望\":\"" + user.威望 + "\",");
                }

                else if (info.Equals("时之券"))
                {
                    json.Append("\"时之券\":\"" + user.时之券 + "\",");
                }

                else if (info.Equals("VIP积分"))
                {
                    json.Append("\"VIP积分\":\"" + user.VIP积分 + "\",");
                }
                else if (info.Equals("至尊VIP")|| info.Equals("星辰VIP"))
                {
                    string time_ = DateTime.Now.ToString("MMdd");
                    bool _61 = false;//儿童节
                    if (time_.Equals("0601"))
                    {
                        _61 = true;
                    }
                    if (user.星辰VIP)
                    {
                        json.Append($"\"VIP特权\":\"{(_61 ? "手握星辰的大儿童" : "星辰VIP")}\",");
                    }
                    else if (user.至尊VIP)
                    {
                        json.Append($"\"VIP特权\":\"{(_61 ? "身怀至尊骨的大儿童" : "至尊VIP")}\",");
                    }
                    else
                    {
                        json.Append($"\"VIP特权\":\"{(_61 ? "普通大儿童" : "未激活")}\",");
                    }
                }
                else if (info.Equals("vip"))
                {
                    json.Append("\"vip\":\"" + user.vip + "\",");
                }

                else if (info.Equals("道具容量"))
                {
                    json.Append("\"道具容量\":\"" + user.道具容量 + "\",");
                }

                else if (info.Equals("宠物数量"))
                {
                    json.Append("\"宠物数量\":\"" + user.宠物数量 + "\",");
                }

                else if (info.Equals("称号"))
                {
                    json.Append("\"称号\":\"" + user.称号 + "\",");
                }

                else if (info.Equals("刷怪数"))
                {
                    json.Append("\"刷怪数\":\"" + user.刷怪数 + "\",");
                }

                else if (info.Equals("自动战斗次数"))
                {
                    json.Append("\"自动战斗次数\":\"" + user.自动战斗次数 + "\",");
                }

                else if (info.Equals("主宠名字"))
                {
                    json.Append("\"主宠名字\":\"" + user.主宠名字 + "\",");
                }
                else if (info.Equals("自动涅槃次数"))
                {
                    json.Append("\"自动涅槃次数\":\"" + user.AutoTime + "\",");
                }
                else if (info.Equals("自动涅槃经验"))
                {
                    if (Convert.ToInt64(user.AutoExp) >= 100000000)
                    {
                        json.Append("\"自动涅槃经验\":\"" + (Convert.ToInt64(user.AutoExp) / 100000000) + "亿\",");
                    }
                    else if (Convert.ToInt64(user.AutoExp) >= 10000)
                    {
                        json.Append("\"自动涅槃经验\":\"" + (Convert.ToInt64(user.AutoExp) / 10000) + "万\",");
                    }
                    else
                    {
                        json.Append("\"自动涅槃经验\":\"" + user.AutoExp + "\",");
                    }

                }
                else if (info.Equals("巫族涅槃经验"))
                {
                    if (Convert.ToInt64(user.AutoExp2) >= 100000000)
                    {
                        json.Append("\"巫族涅槃经验\":\"" + (Convert.ToInt64(user.AutoExp2) / 100000000) + "亿\",");
                    }
                    else if (Convert.ToInt64(user.AutoExp2) >= 10000)
                    {
                        json.Append("\"巫族涅槃经验\":\"" + (Convert.ToInt64(user.AutoExp2) / 10000) + "万\",");
                    }
                    else
                    {
                        json.Append("\"巫族涅槃经验\":\"" + user.AutoExp2 + "\",");
                    }

                }
            }
            json.Append("}");
            json.Replace(",}", "}");
            return json.ToString();

        }


        
        public string EntityToJson(dynamic e)
        {
            return JsonConvert.SerializeObject(e);
            // {"ID":"1","Name":"张三","Age":"20"}
            /*try
            {
                Type t1 = e.GetType();
                PropertyInfo[] pList = t1.GetProperties();
                String JSON = "{";
                foreach (PropertyInfo p in pList)
                {
                    if (p.Name.IndexOf("_") != -1)
                    {
                        continue;
                    }
                    if (show || t1.GetProperty(p.Name).GetValue(e) != null)
                    {

                        dynamic value = t;
                        if (show)
                        {
                            if (t1.GetProperty(p.Name).GetValue(e) == null)
                            {
                                value = t;
                            }
                            else
                            {
                                value = t1.GetProperty(p.Name).GetValue(e).ToString();
                            }
                        }
                        JSON += "\"" + p.Name + "\"";
                        JSON += ":";
                        JSON += "\"" + value + "\"";
                        if (p != pList[pList.Length - 1])
                        {
                            JSON += ",";
                        }
                    }

                }

                JSON += "}";
                JSON = JSON.Replace(",}", "}");
                return JSON;
            }
            catch (Exception EX)
            {
                return null;
            }*/

        }
        /*public string DataTableJsone(DataTable dt)
        {
            if (dt == null || dt.Rows.Count == 0)
                return "[]";
            StringBuilder jsonBuilder = new StringBuilder();
            jsonBuilder.Append("{\"data\":[");
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                jsonBuilder.Append("{");
                for (int j = 0; j < dt.Columns.Count; j++)
                {
                    if (dt.Columns[j].ColumnName != "Row")
                    {
                        jsonBuilder.Append("\"");
                        jsonBuilder.Append(dt.Columns[j].ColumnName);
                        jsonBuilder.Append("\":\"");
                        string value = dt.Rows[i][j].ToString().Replace("\r", "").Replace("\n", "").Replace("\t", "").Trim();
                        if (dt.Columns[j].DataType.FullName == "System.DateTime")
                            jsonBuilder.Append(value.Length == 0 ? value : Convert.ToDateTime(value).ToString("yyyy-MM-dd HH:mm:ss"));
                        else
                            jsonBuilder.Append(value);
                        jsonBuilder.Append("\",");
                    }
                }
                jsonBuilder.Remove(jsonBuilder.Length - 1, 1);
                jsonBuilder.Append("},");
            }
            jsonBuilder.Remove(jsonBuilder.Length - 1, 1);
            jsonBuilder.Append("]}");
            return jsonBuilder.ToString();
        }

        public string MessageInfo(string str)
        {
            try
            {
                string strs = "";
                strs = "{\"data\":{\"Message\":\"" + str + "\"}}";
                return strs;
            }
            catch (Exception)
            {

                throw;
            }

        }


        /// <summary>
        /// 字符串集合转换为JSON
        /// </summary>
        /// <param name="List">字符串集合</param>
        /// <param name="NodeName">根节点名</param>
        /// <returns></returns>
        public string ListToJSON(List<string> List, string NodeName)
        {
            string json = "[";
            foreach (string node in List)
            {
                string Node = "{\"" + NodeName + "\":\"" + node + "\"},";
                json += Node;
            }
            json += "]";
            json = json.Replace("},]", "}]");

            return json;

        }

        /// <summary>
        /// 将一定规则的字符串转换为JSON
        /// </summary>
        /// <param name="str">字符串,格式:节点名:节点值,节点名:节点值;例如:mode:0,msg:信息</param>
        /// <returns></returns>
        public string StrToJSON(string str)
        {
            string json = "{";
            string[] strList = str.Split(',');

            foreach (string strSplit in strList)
            {
                string[] strSplitNode = strSplit.Split(':');
                if (strSplitNode.Length == 2)
                {
                    string Node = "\"" + strSplitNode[0] + "\":\"" + strSplitNode[1] + "\",";
                    json += Node;
                }
            }

            json += "}";
            json = json.Replace(",}", "}");
            return json;
        }*/

        /*internal string JsonContent(Uri url,int timeout = 5000)
        {
            try
            {
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
                request.Timeout = timeout;

                request.ServicePoint.Expect100Continue = false;

                request.ServicePoint.UseNagleAlgorithm = false;
                request.ServicePoint.ConnectionLimit = 65500;
                request.AllowWriteStreamBuffering = false;
                request.Proxy = null;

                HttpWebResponse respone = (HttpWebResponse)request.GetResponse();
                StreamReader stream = new StreamReader(respone.GetResponseStream() ?? throw new InvalidOperationException(), Encoding.GetEncoding("gb2312"));
                string jsonText = stream.ReadToEnd();
                stream.Close();
                respone.Close();

                return jsonText;
            }
            catch (Exception)
            {
                return null;
            }
        }*/

        internal string JsonContent(Uri url)
        {
            
            try
            {
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
                request.CookieContainer = DZ.cc;
                request.ServicePoint.Expect100Continue = false;

                request.ServicePoint.UseNagleAlgorithm = false;
                request.ServicePoint.ConnectionLimit = 65500;
                request.AllowWriteStreamBuffering = false;
                //request.Proxy = null;
                // 设置超时时间为3秒
                request.Timeout = 3000; // 毫秒为单位

                HttpWebResponse respone = (HttpWebResponse)request.GetResponse();
                StreamReader stream = new StreamReader(respone.GetResponseStream() ?? throw new InvalidOperationException(), Encoding.GetEncoding("gb2312"));
                string jsonText = stream.ReadToEnd();
                stream.Close();
                respone.Close();

                return jsonText;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /*internal string JsonContent(Uri url, string data)
        {
            try
            {
                string postData = data; // 要发放的数据 
                byte[] byteArray = Encoding.UTF8.GetBytes(postData);

                HttpWebRequest objWebRequest = (HttpWebRequest)WebRequest.Create(url);
                objWebRequest.CookieContainer = DZ.cc;
                objWebRequest.Method = "POST";
                objWebRequest.ContentType = "application/x-www-form-urlencoded";
                objWebRequest.ContentLength = byteArray.Length;

                Stream newStream = objWebRequest.GetRequestStream();
                // Send the data. 
                newStream.Write(byteArray, 0, byteArray.Length); //写入参数 
                newStream.Close();

                HttpWebResponse response = (HttpWebResponse)objWebRequest.GetResponse();
                StreamReader sr = new StreamReader(response.GetResponseStream() ?? throw new InvalidOperationException(), Encoding.Default);
                string textResponse = sr.ReadToEnd(); // 返回的数据
                return textResponse;
            }
            catch (WebException)
            {
                return null;
            }
            catch (Exception)
            {
                return null;
            }
            
        }*/

        /*internal string JsonContent(Uri url, string data, CookieContainer cc)
        {

            string postData = data; // 要发放的数据 
            byte[] byteArray = Encoding.UTF8.GetBytes(postData);

            HttpWebRequest objWebRequest = (HttpWebRequest)WebRequest.Create(url);
            objWebRequest.CookieContainer = DZ.cc;
            objWebRequest.Method = "POST";
            objWebRequest.ContentType = "application/x-www-form-urlencoded";
            objWebRequest.ContentLength = byteArray.Length;


            Stream newStream = objWebRequest.GetRequestStream();
            // Send the data. 
            newStream.Write(byteArray, 0, byteArray.Length); //写入参数 
            newStream.Close();

            HttpWebResponse response = (HttpWebResponse)objWebRequest.GetResponse();
            StreamReader sr = new StreamReader(response.GetResponseStream() ?? throw new InvalidOperationException(), Encoding.Default);
            string textResponse = sr.ReadToEnd(); // 返回的数据
            return textResponse;
        }*/

        public string GetWeb(string url, int method = 0, string param = "")
        {
            try
            {
                url = UrlEncode(url);
                var client = new SkWeb.SkWebClient
                {
                    //Console.WriteLine(url);
                    // Add a user agent header in case the 
                    // requested URI contains a query.
                    Proxy = null,
                    Timeout = 30000 // 设置超时时间为 30 秒（单位：毫秒）
                };
                client.Headers.Add("user-agent", "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.0; .NET CLR 4.0.30319;)");
                client.Credentials = CredentialCache.DefaultCredentials;
                //byte[] pagedata = client.DownloadData(@url);

                //string result = Encoding.UTF8.GetString(pagedata);
                string result = "";

                client.Headers.Add("Content-Type", "application/x-www-form-urlencoded");
                client.Cookies = DZ.cc;
                while (true)
                {
                    try
                    {
                        if (method == 0)
                        {
                            result = client.DownloadString(url);
                        }
                        else
                        {
                            param = UrlEncode(param);
                            byte[] sendData = Encoding.GetEncoding("UTF-8").GetBytes(param);
                            client.Headers.Add("ContentLength", sendData.Length.ToString());
                            result = client.UploadString(url, "POST", param);
                        }

                        break;
                    }
                    catch (WebException we)
                    {
                        if (we.Status == WebExceptionStatus.Timeout)
                        {
                            if (url.IndexOf("use", StringComparison.Ordinal) > -1)
                            {
                                return "使用成功！";
                            }
                            else
                            {
                                //Console.WriteLine("超时！");
                                continue;
                            }
                        }
                        else
                        {
                            //Console.WriteLine(we.Status);
                            return "";
                        }
                    }
                }
                DZ.cc = client.Cookies;

                return result;
            }
            catch (WebException we)
            {
                if (we.Status == WebExceptionStatus.Timeout)
                {
                    // 处理超时
                    return null; // 返回 null 表示超时
                }
                else
                {
                    // 处理其他类型的异常
                    return null;
                }
            }

        }

        public string GetWeb_UTF8(string url, int method = 0, string param = "")
        {
            try
            {
                url = UrlEncode_UTF8(url);
                var client = new SkWeb.SkWebClient
                {
                    //Console.WriteLine(url);
                    // Add a user agent header in case the 
                    // requested URI contains a query.
                    Proxy = null,
                    Timeout = 30000 // 设置超时时间为 30 秒（单位：毫秒）
                };
                client.Headers.Add("user-agent", "Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.0; .NET CLR 4.0.30319;)");
                client.Credentials = CredentialCache.DefaultCredentials;
                //byte[] pagedata = client.DownloadData(@url);

                //string result = Encoding.UTF8.GetString(pagedata);
                string result = "";
                client.Encoding = Encoding.UTF8;
                client.Headers.Add("Content-Type", "application/x-www-form-urlencoded");
                client.Cookies = DZ.cc;
                while (true)
                {
                    try
                    {
                        if (method == 0)
                        {
                            result = client.DownloadString(url);
                        }
                        else
                        {
                            param = UrlEncode_UTF8(param);
                            byte[] sendData = Encoding.GetEncoding("UTF-8").GetBytes(param);
                            client.Headers.Add("ContentLength", sendData.Length.ToString());
                            result = client.UploadString(url, "POST", param);
                        }

                        break;
                    }
                    catch (WebException we)
                    {
                        if (we.Status == WebExceptionStatus.Timeout)
                        {
                            if (url.IndexOf("use", StringComparison.Ordinal) > -1)
                            {
                                return "使用成功！";
                            }
                            else
                            {
                                //Console.WriteLine("超时！");
                                continue;
                            }
                        }
                        else
                        {
                            //Console.WriteLine(we.Status);
                            return "";
                        }
                    }
                }
                DZ.cc = client.Cookies;

                return result;
            }
            catch (WebException we)
            {
                if (we.Status == WebExceptionStatus.Timeout)
                {
                    // 处理超时
                    return null; // 返回 null 表示超时
                }
                else
                {
                    // 处理其他类型的异常
                    return null;
                }
            }
        }

        protected string UrlEncode(string url)
        {
            byte[] bs = Encoding.GetEncoding("GB2312").GetBytes(url);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < bs.Length; i++)
            {
                if (bs[i] < 128)
                    sb.Append((char)bs[i]);
                else
                {
                    sb.Append("%" + bs[i++].ToString("x").PadLeft(2, '0'));
                    sb.Append("%" + bs[i].ToString("x").PadLeft(2, '0'));
                }
            }
            return sb.ToString();
        }

        protected string UrlEncode_UTF8(string url)
        {
            byte[] bs = Encoding.GetEncoding("UTF-8").GetBytes(url);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < bs.Length; i++)
            {
                if (bs[i] < 128)
                    sb.Append((char)bs[i]);
                else
                {
                    sb.Append("%" + bs[i++].ToString("x").PadLeft(2, '0'));
                    sb.Append("%" + bs[i].ToString("x").PadLeft(2, '0'));
                }
            }
            return sb.ToString();
        }


    }
}