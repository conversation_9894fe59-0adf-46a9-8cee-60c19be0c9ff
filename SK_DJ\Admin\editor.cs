﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using PetShikong;

namespace Admin
{
    public partial class editor : Form
    {
        public editor()
        {
            InitializeComponent();
        }
        List<道具类型> 所有道具=new List<道具类型>();
        string 道具使用类型;
        道具类型 现编辑道具 = new 道具类型();
        道具具体信息 现编辑道具具体信息 = new 道具具体信息();
        private void textBox1_TextChanged(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            所有道具 = new 数据处理().读取所有道具类型();
            foreach(道具类型 道具 in 所有道具)
            {
                string[] str = { 道具.道具序号, 道具.道具名字 };
                dataGridView1.Rows.Add(str);
            }
            label4.Text = "道具数：" + dataGridView1.Rows.Count;


        }

        private void button2_Click(object sender, EventArgs e)
        {
            string[] str = { dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString(), dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["name"].Value.ToString(),"1"};
            dataGridView2.Rows.Add(str);
            
        }

        private void button3_Click(object sender, EventArgs e)
        {
            dataGridView2.Rows.Remove(dataGridView2.CurrentRow);
        }

        private void button5_Click(object sender, EventArgs e)
        {
            现编辑道具.道具名字 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["name"].Value.ToString();
            现编辑道具.道具序号 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString();
            现编辑道具具体信息 = new 数据处理().读取道具脚本(现编辑道具.道具序号);
            textBox1.Text = 现编辑道具具体信息.道具脚本;
            label2.Text="现编辑道具："+ 现编辑道具.道具名字 + "("+ 现编辑道具.道具序号 + ")";


        }

        private void button7_Click(object sender, EventArgs e)
        {
            textBox1.Text = textBox1.Text.Replace("\r\n", "");
            if (textBox1.Text != "")
            {
                string[] array = textBox1.Text.Split('|');
                string[] array2;
                
                int i = 0;
                foreach(string 字符 in array)
                {
                    if (i == 0)
                    {
                        道具使用类型 = 字符;
                        if (字符 == "一定概率获得" || 字符 == "随机获得")
                        {
                            dataGridView2.Rows.Clear();
                            i++;

                        }else
                        {
                            return;
                        }
                        
                    }
                    else
                    {
                        array2 = 字符.Split(',');
                        if (array2.Length > 1)
                        {
                            string[] str ={array2[0],获取道具名称(array2[0]),array2[1]};

                            dataGridView2.Rows.Add(str);
                        }else
                        {
                            string[] str = { array2[0], 获取道具名称(array2[0]), "1" };

                            dataGridView2.Rows.Add(str);
                        }


                    }
                }
            }
        }

        public string 获取道具名称(string 道具编号)
        {
            foreach(道具类型 道具 in 所有道具)
            {
                if (道具.道具序号.Equals(道具编号))
                {
                    return 道具.道具名字;
                }
            }
            return "";
        }

        private void button6_Click(object sender, EventArgs e)
        {
            if (道具使用类型 != "" && (道具使用类型=="一定概率获得" || 道具使用类型 == "随机获得"))
            {
                string 文本=道具使用类型;
                foreach(DataGridViewRow dgvr in dataGridView2.Rows)
                {
                    if (dgvr.Cells[2].Value.ToString() == "1")
                    {
                        文本 = 文本 + "|" + dgvr.Cells[0].Value.ToString();
                    }else
                    {
                        文本 = 文本 + "|" + dgvr.Cells[0].Value.ToString()+","+ dgvr.Cells[2].Value.ToString();
                    }
                    


                }
                textBox1.Text = 文本;
            }
        }

        private void dataGridView1_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            现编辑道具.道具名字 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["name"].Value.ToString();
            现编辑道具.道具序号 = dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString();
            现编辑道具具体信息 = new 数据处理().读取道具脚本(现编辑道具.道具序号);
            textBox1.Text = 现编辑道具具体信息.道具脚本;
            textBox3.Text = 现编辑道具具体信息.道具说明;
            label2.Text = "现编辑道具：" + 现编辑道具.道具名字 + "(" + 现编辑道具.道具序号 + ")";
        }

        private void button4_Click(object sender, EventArgs e)
        {
            道具具体信息 具体信息 = new 道具具体信息();
            具体信息.道具序号 = 现编辑道具.道具序号;
            具体信息.道具脚本 = textBox1.Text;
            具体信息.道具说明 = textBox3.Text;
    //        new 数据处理().保存文件(RC4.EncryptRC4wq(new ConvertJson().EntityToJSON(具体信息), @"qiqiwan.2016.2017.2018.2020.2021.2022"), @"PageMain\propTable\"+ 数据处理.GetStringHash(RC4.EncryptRC4wq((Int32.Parse(现编辑道具.道具序号) + 14).ToString(), 数据处理.获取密钥(2))) + ".data");
            现编辑道具具体信息 = new 数据处理().读取道具脚本(现编辑道具.道具序号);
            textBox1.Text = 现编辑道具具体信息.道具脚本;
            textBox3.Text = 现编辑道具具体信息.道具说明;

        }

        private void button8_Click(object sender, EventArgs e)
        {
            dataGridView1.Rows.Clear();
            foreach (道具类型 道具 in 所有道具)
            {
                if(道具.道具名字 .IndexOf(textBox2.Text)>-1 || 道具.道具序号.IndexOf(textBox2.Text) > -1)
                {
                    string[] str = { 道具.道具序号, 道具.道具名字 };
                    dataGridView1.Rows.Add(str);
                }
                
            }
            label4.Text = "道具数：" + dataGridView1.Rows.Count;
        }

        private void editor_Load(object sender, EventArgs e)
        {

        }
    }
}
