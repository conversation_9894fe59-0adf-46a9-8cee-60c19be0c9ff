﻿using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace Shikong.Pokemon2.PCG
{
    public class PetInfo
    {
        internal bool 宠物素材 { get; set; }
        public string 指定形象 {
            get
            {
                if (宠物序号 == null) return null;
                return new DataProcess().ReadUserInfo().佩戴皮肤;
            }
        }
        internal bool 是否水平翻转 { get; set; }
        public string 宠物序号 { get; set; }
        public string 形象 { get; set; }
        public string 指定五行 = null;

        public string 五行
        {
            get
            {
                if (地狱之门)
                {
                    return "魔";
                }

                if (TTT)
                {
                    return "仙";
                }
                if (世界BOSS)
                {
                    return "仙";
                }

                if (宠物序号 != null)
                {
                    if (指定五行 != null)
                    {
                        return 指定五行;
                    }

                    return new DataProcess().GetAppointedPetType(形象).系别;
                }

                if (指定五行 != null)
                {
                    return 指定五行;
                }

                return new DataProcess().GetMonsterWX(形象);
            }
            set { }
        }

        private string _exp;

        public string 当前经验
        {
            get => _exp;
            set
            {
                if (Convert.ToInt64(value) > 94539554417)
                {
                    value = "94539554417";
                }

                _exp = value;
            }
        }

        public string 等级
        {
            get
            {
                if (宠物序号 == null)
                {
                    return 当前经验;
                }

                return new DataProcess().GetLv(Convert.ToInt64(当前经验)).ToString();
            }
            set { }
        }

        public string 抵消;
        public string 加深;
        public string 吸魔;
        public string 吸血;
        [DefaultValue(10)] public string 生命 { get; set; }
        [DefaultValue(10)] public string 魔法 { get; set; }
        [DefaultValue(10)] public string 最大生命 { get; set; }
        [DefaultValue(10)] public string 最大魔法 { get; set; }
        [DefaultValue(10)] public string 攻击 { get; set; }
        [DefaultValue(10)] public string 防御 { get; set; }
        [DefaultValue(10)] public string 命中 { get; set; }
        [DefaultValue(10)] public string 闪避 { get; set; }
        [DefaultValue(10)] public string 速度 { get; set; }
        public string 状态 { get; set; }
        public bool 地狱之门;
        public bool TTT;
        public bool 世界BOSS;
        public string 自定义宠物名字;

        public string 宠物名字
        {
            get
            {
                if (宠物序号 != null)
                {
                    if (string.IsNullOrEmpty(自定义宠物名字) || 自定义宠物名字.Equals("0"))
                    {
                        return new DataProcess().GetAppointedPetType(形象).宠物名字;
                    }

                    return 自定义宠物名字;
                }

                if (地狱之门)
                {
                    return "魔化的" + new DataProcess().GetMonsterName(形象);
                }

                if (TTT)
                {
                    return "仙·" + new DataProcess().GetMonsterName(形象);
                }
                if (世界BOSS)
                {
                    string[] BOSSinfo = new ConvertJson().GetWeb("http://whale-fall.info/sk/BOSSConfig/info.ini").Split('|');
                    return BOSSinfo[0];
                }
                if (自定义宠物名字 != null)
                {
                    return 自定义宠物名字;
                }

                return new DataProcess().GetMonsterName(形象);
            }
            set { }
        }


        [DefaultValue(0)] private string _成长;

        public string 成长
        {
            get => _成长;
            set
            {
                // 使用动态成长上限
                double maxCC = string.IsNullOrEmpty(成长上限) ? 20000000 : Convert.ToDouble(成长上限);
                if (Convert.ToDouble(value) > maxCC)
                {
                    value = maxCC.ToString();
                }

                _成长 = value;
                ;
            }
        }

        // 成长突破相关字段
        [DefaultValue("20000000")]
        public string 成长上限 { get; set; } = "20000000"; // 默认2000万
        [DefaultValue("0")]
        public string 成长突破等级 { get; set; } = "0";
        [DefaultValue("0")]
        public string 成长突破累计成功率 { get; set; } = "0";

        public string 已进化次数 { get; set; }
        [DefaultValue("")] public string 技能列表 { get; set; }

        public string 技能显示//技能显示 技能倍数 BUFF倍数 
        {
            get
            {
                if (技能列表 != null)
                {
                    string str = "";
                    foreach (SkillInfo 技 in 信息)
                    {
                        if (技.信息 != null)
                        {
                            str += "," + 技.信息.技能名字 + "|" + 技.技能等级 + "|" + 技.技能序号 + "|" + 技.信息.耗蓝量 + "|" + 技.信息.BUFF + "|";
                            if (技.信息.附带效果增量 != null && 技.信息.附带效果增量.ToLower() != "null")
                            {
                                double skval = Convert.ToDouble(技.信息.附带效果增量);
                                //skval *= 1 + Convert.ToInt32(技.技能等级) * 0.05;//1+18*0.005
                                //下标5为详细信息

                                skval = skval + (Convert.ToInt32(技.技能等级) * 0.005);
                                str += "+" + (skval * 100) + "%" + 技.信息.技能附带效果;
                            }
                            else {//主动技能
                                double skval = 1 + Convert.ToDouble(技.信息.技能百分比);
                                skval = skval + (Convert.ToInt32(技.技能等级) * 0.02);
                                str += (skval * 100) + "%";
                            }


                        }
                    }

                    return str;
                }

                return "";
            }
            set { }
        }

        public List<SkillInfo> 信息
        {
            get
            {
                if (!string.IsNullOrEmpty(技能列表))
                {
                    string[] dats = 技能列表.Split(',');
                    List<SkillInfo> 信息 = new List<SkillInfo>();
                    foreach (string d in dats)
                    {
                        string[] datss = d.Split('|');
                        if (datss.Length >= 2)
                        {
                            SkillInfo 技能 = new SkillInfo();
                            技能.技能等级 = datss[2];
                            技能.技能序号 = datss[1];
                            信息.Add(技能);
                        }
                    }

                    return 信息;
                }

                return new List<SkillInfo>();
            }
            set { }
        }

        /*public string ToMd5()
        {
            string md5 = 成长 + 五行 + 加深 + 吸血 + 吸魔 + 命中 + 形象 + 成长 +
                         抵消 + 攻击 + 生命 + 速度 + 闪避 + 防御 + 魔法;
            return DataProcess.GetStringHash(md5);
        }*/

        public string 境界 { get; set; }

        public string TalismanState { get; set; }

        /// <summary>
        /// 获取当前的成长上限
        /// </summary>
        /// <returns>成长上限值</returns>
        public double GetGrowthLimit()
        {
            if (string.IsNullOrEmpty(成长上限) || 成长上限 == "0")
            {
                // 根据成长突破等级动态计算默认值
                if (!string.IsNullOrEmpty(成长突破等级) && 成长突破等级 != "0")
                {
                    int level = Convert.ToInt32(成长突破等级);
                    // 根据突破配置计算成长上限
                    var config = Shikong.Pokemon2.PCG.成长突破.BreakthroughConfig.GetConfig(level);
                    if (config != null)
                    {
                        return config.ccMax * 10000; // 配置中是万为单位
                    }
                    // 如果配置不存在，使用简单计算：每级增加100万成长上限
                    return 20000000 + (level * 1000000);
                }

                // 默认成长上限为2000万
                return 20000000;
            }

            return Convert.ToDouble(成长上限);
        }

    }
}
