﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Shikong.Pokemon2.PCG;
using PetShikongTools;
using System.IO;
using System.Text;

namespace Admin
{
    public partial class 装备列表 : Form
    {
        public 装备列表()
        {
            InitializeComponent();
        }

        List<EquipmentType> 道具 = new DataProcess().GetEquipmentList();
        public string 类型 = "ID";
        public TextBox TEXT;
        public TextBox edi;
        public TextBox 文件;
        private void 道具列表_Load(object sender, EventArgs e)
        {
       
            dataGridView1.DataSource = 道具;
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
           
           
        }

        private void dataGridView1_Click(object sender, EventArgs e)
        {
          
        }

        private void dataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
           
        }

        private void dataGridView1_CellContentDoubleClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void dataGridView1_CellMouseDoubleClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            try
            {
                if (
                    e.RowIndex > -1)
                {
                    if (类型.Equals("ID"))
                    {
                        TEXT.Text = 道具[e.RowIndex].ID;
                    }
                    else
                    {
                        TEXT.Text = 道具[e.RowIndex].名字;
                    }
                    Close();
                }
            }
            catch (Exception ex)
            {

            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridView1.SelectedRows.Count > 0)
                {
                    string 序号 = "";
                    foreach (DataGridViewRow r in dataGridView1.SelectedRows)
                    {
                        if (类型.Equals("ID"))
                        {
                            序号 = 序号 + r.Cells["装备序号"].Value + "|";
                            文件.Text = DataProcess.EDC_Path +SkCryptography.GetHash.GetStringHash(SkRC4.DES.EncryptRC4(r.Cells["装备序号"].Value.ToString(), GetKey(2))) + ".dat";
                            String 文件名 =文件.Text;
                            //textBox1.Text = 文件名;
                            //根据文件名判断是否为存档文件，若为配置文件则应更改i
                            var filenames = 文件名;

                            using (StreamReader sr = new StreamReader(文件名, Encoding.Default))
                            {
                              
                                //textBox1.Text = 文件名;
                                //根据文件名判断是否为存档文件，若为配置文件则应更改i
                                FileInfo fi = new FileInfo(文件名);
                                文件.Text = 文件名;
                                Text = fi.Name;
                                edi.Text = SkRC4.DES.DecryptRC4(sr.ReadToEnd(), @"qiqiwan.2016.2017.2018.2020.2021.2022");


                            }
                        }
                        else {
                            序号 = 序号 + r.Cells[类型].Value + "、";
                        }
                    }
                    序号 = 序号 + "|";
                    序号 = 序号.Replace( "||","");
                    序号 = 序号.Replace("、|", "");
                    //TEXT.Text += "|" + 序号;
                    Close();
                }
            }
            catch (Exception ex)
            {

            }
        }
        private const string VKey = "HYFMWZS";
        private const string Key11 = "XGVUSZF";
        private const string Key12 = "LjIwMTYuMjAxNy4yMDE4LjIwMjAuMjAyMS4yMDIy";
        private const string Key21 = "JYSWQRT";
        private const string Key22 = "LjEyMzQuNTY3OC45MDEyLjM0NTYuNzg5MC5hYmNk";
        public string GetKey(int keyIndex)
        {
            if (keyIndex == 1)
            {
                return SkCryptography.Vigenere.de(Key11, VKey).ToLower() + SkCryptography.Base64.DecodeBase64("LjIwMTYuMjAxNy4yMDE4LjIwMjAuMjAyMS4yMDIy");
            }

            if (keyIndex == 2)
            {
                return SkCryptography.Vigenere.de(Key21, VKey).ToLower() + SkCryptography.Base64.DecodeBase64("LjEyMzQuNTY3OC45MDEyLjM0NTYuNzg5MC5hYmNk");
            }

            return null;
        }

        private void textBox1_TextChanged(object sender, EventArgs e)
        {
            List<EquipmentType> 新列表 = new List<EquipmentType>();
            for (int i = 0; i < 道具.Count; i++)
            {
                if (道具[i].名字.IndexOf(textBox1.Text) != -1 || textBox1.Text.Length == 0)
                {
                    新列表.Add(道具[i]);
                }
            }
            dataGridView1.DataSource = 新列表;
        }

        private void label1_Click(object sender, EventArgs e)
        {

        }
    }
}
