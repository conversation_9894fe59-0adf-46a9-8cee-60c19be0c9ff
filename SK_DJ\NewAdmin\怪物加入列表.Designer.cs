﻿namespace Admin
{
    partial class 怪物加入列表
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.怪物名字 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.序号 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.最小等级 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.最大等级 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.最大掉落 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.怪物五行 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.怪物成长 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.经验值 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToOrderColumns = true;
            this.dataGridView1.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dataGridView1.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.怪物名字,
            this.序号,
            this.最小等级,
            this.最大等级,
            this.最大掉落,
            this.怪物五行,
            this.怪物成长,
            this.经验值});
            this.dataGridView1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridView1.Location = new System.Drawing.Point(0, 0);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.RowTemplate.Height = 23;
            this.dataGridView1.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.dataGridView1.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView1.Size = new System.Drawing.Size(845, 283);
            this.dataGridView1.TabIndex = 1;
            this.dataGridView1.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView1_CellContentClick);
            this.dataGridView1.CellMouseDoubleClick += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.dataGridView1_CellMouseDoubleClick);
            // 
            // 怪物名字
            // 
            this.怪物名字.DataPropertyName = "怪物名字";
            this.怪物名字.HeaderText = "怪物名字";
            this.怪物名字.Name = "怪物名字";
            this.怪物名字.ReadOnly = true;
            this.怪物名字.Width = 102;
            // 
            // 序号
            // 
            this.序号.DataPropertyName = "怪物序号";
            this.序号.HeaderText = "怪物序号";
            this.序号.Name = "序号";
            this.序号.ReadOnly = true;
            this.序号.Width = 101;
            // 
            // 最小等级
            // 
            this.最小等级.DataPropertyName = "最小等级";
            this.最小等级.HeaderText = "最小等级";
            this.最小等级.Name = "最小等级";
            // 
            // 最大等级
            // 
            this.最大等级.DataPropertyName = "最大等级";
            this.最大等级.HeaderText = "最大等级";
            this.最大等级.Name = "最大等级";
            // 
            // 最大掉落
            // 
            this.最大掉落.DataPropertyName = "最大掉落";
            this.最大掉落.HeaderText = "最大掉落";
            this.最大掉落.Name = "最大掉落";
            // 
            // 怪物五行
            // 
            this.怪物五行.DataPropertyName = "怪物五行";
            this.怪物五行.HeaderText = "怪物五行";
            this.怪物五行.Name = "怪物五行";
            // 
            // 怪物成长
            // 
            this.怪物成长.DataPropertyName = "怪物成长";
            this.怪物成长.HeaderText = "怪物成长";
            this.怪物成长.Name = "怪物成长";
            // 
            // 经验值
            // 
            this.经验值.DataPropertyName = "经验值";
            this.经验值.HeaderText = "经验值";
            this.经验值.Name = "经验值";
            // 
            // 怪物加入列表
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(845, 283);
            this.Controls.Add(this.dataGridView1);
            this.Name = "怪物加入列表";
            this.Text = "怪物加入列表";
            this.Load += new System.EventHandler(this.怪物加入列表_Load);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.DataGridViewTextBoxColumn 怪物名字;
        private System.Windows.Forms.DataGridViewTextBoxColumn 序号;
        private System.Windows.Forms.DataGridViewTextBoxColumn 最小等级;
        private System.Windows.Forms.DataGridViewTextBoxColumn 最大等级;
        private System.Windows.Forms.DataGridViewTextBoxColumn 最大掉落;
        private System.Windows.Forms.DataGridViewTextBoxColumn 怪物五行;
        private System.Windows.Forms.DataGridViewTextBoxColumn 怪物成长;
        private System.Windows.Forms.DataGridViewTextBoxColumn 经验值;
    }
}