﻿namespace Admin
{
    partial class 管理
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.comboBox1 = new System.Windows.Forms.ComboBox();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.地图ID = new System.Windows.Forms.TextBox();
            this.button1 = new System.Windows.Forms.Button();
            this.文件 = new System.Windows.Forms.TextBox();
            this.button9 = new System.Windows.Forms.Button();
            this.tabPage8 = new System.Windows.Forms.TabPage();
            this.button17 = new System.Windows.Forms.Button();
            this.tabPage6 = new System.Windows.Forms.TabPage();
            this.button13 = new System.Windows.Forms.Button();
            this.linkLabel12 = new System.Windows.Forms.LinkLabel();
            this.label41 = new System.Windows.Forms.Label();
            this.存档装备ID = new System.Windows.Forms.TextBox();
            this.tabPage5 = new System.Windows.Forms.TabPage();
            this.装备特效 = new System.Windows.Forms.TextBox();
            this.label47 = new System.Windows.Forms.Label();
            this.textBox4 = new System.Windows.Forms.TextBox();
            this.装备命中 = new System.Windows.Forms.TextBox();
            this.装备图标 = new System.Windows.Forms.TextBox();
            this.装备吸血 = new System.Windows.Forms.TextBox();
            this.装备加深 = new System.Windows.Forms.TextBox();
            this.装备吸魔 = new System.Windows.Forms.TextBox();
            this.装备抵消 = new System.Windows.Forms.TextBox();
            this.装备魔法 = new System.Windows.Forms.TextBox();
            this.装备生命 = new System.Windows.Forms.TextBox();
            this.装备闪避 = new System.Windows.Forms.TextBox();
            this.装备速度 = new System.Windows.Forms.TextBox();
            this.装备防御 = new System.Windows.Forms.TextBox();
            this.装备攻击 = new System.Windows.Forms.TextBox();
            this.装备名字 = new System.Windows.Forms.TextBox();
            this.装备_ID = new System.Windows.Forms.TextBox();
            this.label42 = new System.Windows.Forms.Label();
            this.button12 = new System.Windows.Forms.Button();
            this.主属性 = new System.Windows.Forms.ComboBox();
            this.label46 = new System.Windows.Forms.Label();
            this.装备类型 = new System.Windows.Forms.ComboBox();
            this.label40 = new System.Windows.Forms.Label();
            this.label43 = new System.Windows.Forms.Label();
            this.label39 = new System.Windows.Forms.Label();
            this.label38 = new System.Windows.Forms.Label();
            this.label35 = new System.Windows.Forms.Label();
            this.label37 = new System.Windows.Forms.Label();
            this.label36 = new System.Windows.Forms.Label();
            this.label34 = new System.Windows.Forms.Label();
            this.label33 = new System.Windows.Forms.Label();
            this.label32 = new System.Windows.Forms.Label();
            this.label31 = new System.Windows.Forms.Label();
            this.label30 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.label28 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.tabControl2 = new System.Windows.Forms.TabControl();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.textBox6 = new System.Windows.Forms.TextBox();
            this.label45 = new System.Windows.Forms.Label();
            this.checkBox2 = new System.Windows.Forms.CheckBox();
            this.label44 = new System.Windows.Forms.Label();
            this.textBox5 = new System.Windows.Forms.TextBox();
            this.linkLabel5 = new System.Windows.Forms.LinkLabel();
            this.button5 = new System.Windows.Forms.Button();
            this.label17 = new System.Windows.Forms.Label();
            this.地图掉落最大数量 = new System.Windows.Forms.TextBox();
            this.label18 = new System.Windows.Forms.Label();
            this.地图掉落最小数量 = new System.Windows.Forms.TextBox();
            this.地图掉落列表 = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.地图最大元宝 = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.地图最小元宝 = new System.Windows.Forms.TextBox();
            this.label14 = new System.Windows.Forms.Label();
            this.地图最大金币 = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.地图最小金币 = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.地图序号 = new System.Windows.Forms.TextBox();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.button6 = new System.Windows.Forms.Button();
            this.linkLabel9 = new System.Windows.Forms.LinkLabel();
            this.怪物经验 = new System.Windows.Forms.TextBox();
            this.label25 = new System.Windows.Forms.Label();
            this.button8 = new System.Windows.Forms.Button();
            this.linkLabel7 = new System.Windows.Forms.LinkLabel();
            this.linkLabel8 = new System.Windows.Forms.LinkLabel();
            this.button7 = new System.Windows.Forms.Button();
            this.怪物掉落 = new System.Windows.Forms.TextBox();
            this.label24 = new System.Windows.Forms.Label();
            this.怪物成长 = new System.Windows.Forms.TextBox();
            this.怪物最小等级 = new System.Windows.Forms.TextBox();
            this.怪物序号 = new System.Windows.Forms.TextBox();
            this.怪物最大掉落 = new System.Windows.Forms.TextBox();
            this.label23 = new System.Windows.Forms.Label();
            this.linkLabel6 = new System.Windows.Forms.LinkLabel();
            this.label22 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.怪物最大等级 = new System.Windows.Forms.TextBox();
            this.label21 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.宠物存档 = new System.Windows.Forms.TabPage();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.button4 = new System.Windows.Forms.Button();
            this.linkLabel4 = new System.Windows.Forms.LinkLabel();
            this.label9 = new System.Windows.Forms.Label();
            this.宠物成长 = new System.Windows.Forms.TextBox();
            this.宠物序号 = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.道具存档 = new System.Windows.Forms.TabPage();
            this.button3 = new System.Windows.Forms.Button();
            this.linkLabel3 = new System.Windows.Forms.LinkLabel();
            this.label8 = new System.Windows.Forms.Label();
            this.添加道具数量 = new System.Windows.Forms.TextBox();
            this.道具序号2 = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.道具配置 = new System.Windows.Forms.TabPage();
            this.linkLabel14 = new System.Windows.Forms.LinkLabel();
            this.linkLabel13 = new System.Windows.Forms.LinkLabel();
            this.label26 = new System.Windows.Forms.Label();
            this.出售价格 = new System.Windows.Forms.TextBox();
            this.道具说明 = new System.Windows.Forms.TextBox();
            this.道具脚本 = new System.Windows.Forms.TextBox();
            this.道具序号 = new System.Windows.Forms.TextBox();
            this.道具图标 = new System.Windows.Forms.TextBox();
            this.道具名字 = new System.Windows.Forms.TextBox();
            this.linkLabel11 = new System.Windows.Forms.LinkLabel();
            this.linkLabel10 = new System.Windows.Forms.LinkLabel();
            this.linkLabel2 = new System.Windows.Forms.LinkLabel();
            this.linkLabel1 = new System.Windows.Forms.LinkLabel();
            this.label6 = new System.Windows.Forms.Label();
            this.button2 = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage7 = new System.Windows.Forms.TabPage();
            this.button16 = new System.Windows.Forms.Button();
            this.button15 = new System.Windows.Forms.Button();
            this.button14 = new System.Windows.Forms.Button();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.button10 = new System.Windows.Forms.Button();
            this.tabPage9 = new System.Windows.Forms.TabPage();
            this.button18 = new System.Windows.Forms.Button();
            this.checkBox1 = new System.Windows.Forms.CheckBox();
            this.button11 = new System.Windows.Forms.Button();
            this.tabPage8.SuspendLayout();
            this.tabPage6.SuspendLayout();
            this.tabPage5.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabControl2.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.宠物存档.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.道具存档.SuspendLayout();
            this.道具配置.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage7.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.SuspendLayout();
            // 
            // comboBox1
            // 
            this.comboBox1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBox1.FormattingEnabled = true;
            this.comboBox1.Items.AddRange(new object[] {
            "宠物配置",
            "怪物配置",
            "道具配置",
            "技能配置",
            "人物存档",
            "宠物存档",
            "道具存档",
            "装备存档",
            "任务存档",
            "副本进度存档",
            "地图定义存档",
            "地图怪物定义存档"});
            this.comboBox1.Location = new System.Drawing.Point(14, 12);
            this.comboBox1.Name = "comboBox1";
            this.comboBox1.Size = new System.Drawing.Size(440, 20);
            this.comboBox1.TabIndex = 0;
            this.comboBox1.SelectedIndexChanged += new System.EventHandler(this.comboBox1_SelectedIndexChanged);
            // 
            // textBox1
            // 
            this.textBox1.AllowDrop = true;
            this.textBox1.Location = new System.Drawing.Point(13, 38);
            this.textBox1.MaxLength = 99999999;
            this.textBox1.Multiline = true;
            this.textBox1.Name = "textBox1";
            this.textBox1.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox1.Size = new System.Drawing.Size(441, 281);
            this.textBox1.TabIndex = 1;
            this.textBox1.TextChanged += new System.EventHandler(this.textBox1_TextChanged);
            this.textBox1.DragDrop += new System.Windows.Forms.DragEventHandler(this.textBox1_DragDrop);
            this.textBox1.DragEnter += new System.Windows.Forms.DragEventHandler(this.textBox1_DragEnter);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(497, 20);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(47, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "地图ID:";
            // 
            // 地图ID
            // 
            this.地图ID.Location = new System.Drawing.Point(548, 15);
            this.地图ID.Name = "地图ID";
            this.地图ID.Size = new System.Drawing.Size(64, 21);
            this.地图ID.TabIndex = 3;
            this.地图ID.Text = "1";
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(14, 331);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(74, 48);
            this.button1.TabIndex = 4;
            this.button1.Text = "保存修改";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // 文件
            // 
            this.文件.Location = new System.Drawing.Point(629, 15);
            this.文件.Name = "文件";
            this.文件.Size = new System.Drawing.Size(166, 21);
            this.文件.TabIndex = 5;
            // 
            // button9
            // 
            this.button9.Location = new System.Drawing.Point(103, 331);
            this.button9.Name = "button9";
            this.button9.Size = new System.Drawing.Size(76, 48);
            this.button9.TabIndex = 7;
            this.button9.Text = "转换为RC4";
            this.button9.UseVisualStyleBackColor = true;
            this.button9.Click += new System.EventHandler(this.button9_Click);
            // 
            // tabPage8
            // 
            this.tabPage8.Controls.Add(this.button17);
            this.tabPage8.Location = new System.Drawing.Point(4, 22);
            this.tabPage8.Name = "tabPage8";
            this.tabPage8.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage8.Size = new System.Drawing.Size(309, 327);
            this.tabPage8.TabIndex = 9;
            this.tabPage8.Text = "生成操作";
            this.tabPage8.UseVisualStyleBackColor = true;
            // 
            // button17
            // 
            this.button17.Location = new System.Drawing.Point(21, 20);
            this.button17.Name = "button17";
            this.button17.Size = new System.Drawing.Size(127, 45);
            this.button17.TabIndex = 0;
            this.button17.Text = "生成图纸预览界面";
            this.button17.UseVisualStyleBackColor = true;
            this.button17.Click += new System.EventHandler(this.button17_Click);
            // 
            // tabPage6
            // 
            this.tabPage6.Controls.Add(this.button13);
            this.tabPage6.Controls.Add(this.linkLabel12);
            this.tabPage6.Controls.Add(this.label41);
            this.tabPage6.Controls.Add(this.存档装备ID);
            this.tabPage6.Location = new System.Drawing.Point(4, 22);
            this.tabPage6.Name = "tabPage6";
            this.tabPage6.Size = new System.Drawing.Size(309, 327);
            this.tabPage6.TabIndex = 7;
            this.tabPage6.Text = "装备存档";
            this.tabPage6.UseVisualStyleBackColor = true;
            this.tabPage6.Click += new System.EventHandler(this.tabPage6_Click);
            // 
            // button13
            // 
            this.button13.Location = new System.Drawing.Point(44, 115);
            this.button13.Name = "button13";
            this.button13.Size = new System.Drawing.Size(194, 35);
            this.button13.TabIndex = 17;
            this.button13.Text = "添加道具";
            this.button13.UseVisualStyleBackColor = true;
            this.button13.Click += new System.EventHandler(this.button13_Click);
            // 
            // linkLabel12
            // 
            this.linkLabel12.AutoSize = true;
            this.linkLabel12.Location = new System.Drawing.Point(223, 36);
            this.linkLabel12.Name = "linkLabel12";
            this.linkLabel12.Size = new System.Drawing.Size(53, 12);
            this.linkLabel12.TabIndex = 16;
            this.linkLabel12.TabStop = true;
            this.linkLabel12.Text = "选择道具";
            this.linkLabel12.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel12_LinkClicked);
            // 
            // label41
            // 
            this.label41.AutoSize = true;
            this.label41.Location = new System.Drawing.Point(12, 36);
            this.label41.Name = "label41";
            this.label41.Size = new System.Drawing.Size(59, 12);
            this.label41.TabIndex = 15;
            this.label41.Text = "装备序号:";
            // 
            // 存档装备ID
            // 
            this.存档装备ID.Location = new System.Drawing.Point(81, 31);
            this.存档装备ID.Name = "存档装备ID";
            this.存档装备ID.Size = new System.Drawing.Size(134, 21);
            this.存档装备ID.TabIndex = 14;
            // 
            // tabPage5
            // 
            this.tabPage5.Controls.Add(this.装备特效);
            this.tabPage5.Controls.Add(this.label47);
            this.tabPage5.Controls.Add(this.textBox4);
            this.tabPage5.Controls.Add(this.装备命中);
            this.tabPage5.Controls.Add(this.装备图标);
            this.tabPage5.Controls.Add(this.装备吸血);
            this.tabPage5.Controls.Add(this.装备加深);
            this.tabPage5.Controls.Add(this.装备吸魔);
            this.tabPage5.Controls.Add(this.装备抵消);
            this.tabPage5.Controls.Add(this.装备魔法);
            this.tabPage5.Controls.Add(this.装备生命);
            this.tabPage5.Controls.Add(this.装备闪避);
            this.tabPage5.Controls.Add(this.装备速度);
            this.tabPage5.Controls.Add(this.装备防御);
            this.tabPage5.Controls.Add(this.装备攻击);
            this.tabPage5.Controls.Add(this.装备名字);
            this.tabPage5.Controls.Add(this.装备_ID);
            this.tabPage5.Controls.Add(this.label42);
            this.tabPage5.Controls.Add(this.button12);
            this.tabPage5.Controls.Add(this.主属性);
            this.tabPage5.Controls.Add(this.label46);
            this.tabPage5.Controls.Add(this.装备类型);
            this.tabPage5.Controls.Add(this.label40);
            this.tabPage5.Controls.Add(this.label43);
            this.tabPage5.Controls.Add(this.label39);
            this.tabPage5.Controls.Add(this.label38);
            this.tabPage5.Controls.Add(this.label35);
            this.tabPage5.Controls.Add(this.label37);
            this.tabPage5.Controls.Add(this.label36);
            this.tabPage5.Controls.Add(this.label34);
            this.tabPage5.Controls.Add(this.label33);
            this.tabPage5.Controls.Add(this.label32);
            this.tabPage5.Controls.Add(this.label31);
            this.tabPage5.Controls.Add(this.label30);
            this.tabPage5.Controls.Add(this.label29);
            this.tabPage5.Controls.Add(this.label28);
            this.tabPage5.Controls.Add(this.label27);
            this.tabPage5.Location = new System.Drawing.Point(4, 22);
            this.tabPage5.Name = "tabPage5";
            this.tabPage5.Size = new System.Drawing.Size(309, 327);
            this.tabPage5.TabIndex = 6;
            this.tabPage5.Text = "装备管理";
            this.tabPage5.UseVisualStyleBackColor = true;
            // 
            // 装备特效
            // 
            this.装备特效.Location = new System.Drawing.Point(73, 229);
            this.装备特效.Name = "装备特效";
            this.装备特效.Size = new System.Drawing.Size(100, 21);
            this.装备特效.TabIndex = 10;
            // 
            // label47
            // 
            this.label47.AutoSize = true;
            this.label47.Location = new System.Drawing.Point(20, 232);
            this.label47.Name = "label47";
            this.label47.Size = new System.Drawing.Size(29, 12);
            this.label47.TabIndex = 9;
            this.label47.Text = "特效";
            // 
            // textBox4
            // 
            this.textBox4.Location = new System.Drawing.Point(73, 258);
            this.textBox4.Multiline = true;
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new System.Drawing.Size(198, 38);
            this.textBox4.TabIndex = 8;
            // 
            // 装备命中
            // 
            this.装备命中.Location = new System.Drawing.Point(73, 203);
            this.装备命中.Name = "装备命中";
            this.装备命中.Size = new System.Drawing.Size(58, 21);
            this.装备命中.TabIndex = 3;
            // 
            // 装备图标
            // 
            this.装备图标.Location = new System.Drawing.Point(73, 176);
            this.装备图标.Name = "装备图标";
            this.装备图标.Size = new System.Drawing.Size(58, 21);
            this.装备图标.TabIndex = 3;
            // 
            // 装备吸血
            // 
            this.装备吸血.Location = new System.Drawing.Point(73, 149);
            this.装备吸血.Name = "装备吸血";
            this.装备吸血.Size = new System.Drawing.Size(58, 21);
            this.装备吸血.TabIndex = 1;
            // 
            // 装备加深
            // 
            this.装备加深.Location = new System.Drawing.Point(73, 122);
            this.装备加深.Name = "装备加深";
            this.装备加深.Size = new System.Drawing.Size(58, 21);
            this.装备加深.TabIndex = 1;
            // 
            // 装备吸魔
            // 
            this.装备吸魔.Location = new System.Drawing.Point(199, 149);
            this.装备吸魔.Name = "装备吸魔";
            this.装备吸魔.Size = new System.Drawing.Size(58, 21);
            this.装备吸魔.TabIndex = 1;
            // 
            // 装备抵消
            // 
            this.装备抵消.Location = new System.Drawing.Point(199, 122);
            this.装备抵消.Name = "装备抵消";
            this.装备抵消.Size = new System.Drawing.Size(58, 21);
            this.装备抵消.TabIndex = 1;
            // 
            // 装备魔法
            // 
            this.装备魔法.Location = new System.Drawing.Point(199, 95);
            this.装备魔法.Name = "装备魔法";
            this.装备魔法.Size = new System.Drawing.Size(58, 21);
            this.装备魔法.TabIndex = 1;
            // 
            // 装备生命
            // 
            this.装备生命.Location = new System.Drawing.Point(73, 95);
            this.装备生命.Name = "装备生命";
            this.装备生命.Size = new System.Drawing.Size(58, 21);
            this.装备生命.TabIndex = 1;
            // 
            // 装备闪避
            // 
            this.装备闪避.Location = new System.Drawing.Point(199, 68);
            this.装备闪避.Name = "装备闪避";
            this.装备闪避.Size = new System.Drawing.Size(58, 21);
            this.装备闪避.TabIndex = 1;
            // 
            // 装备速度
            // 
            this.装备速度.Location = new System.Drawing.Point(73, 68);
            this.装备速度.Name = "装备速度";
            this.装备速度.Size = new System.Drawing.Size(58, 21);
            this.装备速度.TabIndex = 1;
            // 
            // 装备防御
            // 
            this.装备防御.Location = new System.Drawing.Point(199, 41);
            this.装备防御.Name = "装备防御";
            this.装备防御.Size = new System.Drawing.Size(58, 21);
            this.装备防御.TabIndex = 1;
            // 
            // 装备攻击
            // 
            this.装备攻击.Location = new System.Drawing.Point(73, 41);
            this.装备攻击.Name = "装备攻击";
            this.装备攻击.Size = new System.Drawing.Size(58, 21);
            this.装备攻击.TabIndex = 1;
            // 
            // 装备名字
            // 
            this.装备名字.Location = new System.Drawing.Point(199, 14);
            this.装备名字.Name = "装备名字";
            this.装备名字.Size = new System.Drawing.Size(58, 21);
            this.装备名字.TabIndex = 1;
            // 
            // 装备_ID
            // 
            this.装备_ID.Location = new System.Drawing.Point(73, 14);
            this.装备_ID.Name = "装备_ID";
            this.装备_ID.Size = new System.Drawing.Size(58, 21);
            this.装备_ID.TabIndex = 1;
            // 
            // label42
            // 
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(20, 267);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(35, 12);
            this.label42.TabIndex = 7;
            this.label42.Text = "说明:";
            // 
            // button12
            // 
            this.button12.Location = new System.Drawing.Point(46, 301);
            this.button12.Name = "button12";
            this.button12.Size = new System.Drawing.Size(216, 22);
            this.button12.TabIndex = 6;
            this.button12.Text = "保存";
            this.button12.UseVisualStyleBackColor = true;
            this.button12.Click += new System.EventHandler(this.button12_Click);
            // 
            // 主属性
            // 
            this.主属性.FormattingEnabled = true;
            this.主属性.Items.AddRange(new object[] {
            "攻击",
            "防御",
            "速度",
            "闪避",
            "生命",
            "魔法",
            "命中"});
            this.主属性.Location = new System.Drawing.Point(199, 204);
            this.主属性.Name = "主属性";
            this.主属性.Size = new System.Drawing.Size(72, 20);
            this.主属性.TabIndex = 5;
            // 
            // label46
            // 
            this.label46.AutoSize = true;
            this.label46.Location = new System.Drawing.Point(146, 207);
            this.label46.Name = "label46";
            this.label46.Size = new System.Drawing.Size(47, 12);
            this.label46.TabIndex = 4;
            this.label46.Text = "主属性:";
            // 
            // 装备类型
            // 
            this.装备类型.FormattingEnabled = true;
            this.装备类型.Items.AddRange(new object[] {
            "身体",
            "头部",
            "项链",
            "武器",
            "手镯",
            "脚部",
            "戒指",
            "翅膀",
            "宝石",
            "道具",
            "卡牌左",
            "卡牌右",
            "灵饰",
            "法宝"});
            this.装备类型.Location = new System.Drawing.Point(199, 179);
            this.装备类型.Name = "装备类型";
            this.装备类型.Size = new System.Drawing.Size(72, 20);
            this.装备类型.TabIndex = 5;
            // 
            // label40
            // 
            this.label40.AutoSize = true;
            this.label40.Location = new System.Drawing.Point(146, 179);
            this.label40.Name = "label40";
            this.label40.Size = new System.Drawing.Size(35, 12);
            this.label40.TabIndex = 4;
            this.label40.Text = "类型:";
            // 
            // label43
            // 
            this.label43.AutoSize = true;
            this.label43.Location = new System.Drawing.Point(20, 206);
            this.label43.Name = "label43";
            this.label43.Size = new System.Drawing.Size(35, 12);
            this.label43.TabIndex = 2;
            this.label43.Text = "命中:";
            // 
            // label39
            // 
            this.label39.AutoSize = true;
            this.label39.Location = new System.Drawing.Point(20, 179);
            this.label39.Name = "label39";
            this.label39.Size = new System.Drawing.Size(35, 12);
            this.label39.TabIndex = 2;
            this.label39.Text = "图标:";
            // 
            // label38
            // 
            this.label38.AutoSize = true;
            this.label38.Location = new System.Drawing.Point(20, 152);
            this.label38.Name = "label38";
            this.label38.Size = new System.Drawing.Size(35, 12);
            this.label38.TabIndex = 0;
            this.label38.Text = "吸血:";
            // 
            // label35
            // 
            this.label35.AutoSize = true;
            this.label35.Location = new System.Drawing.Point(20, 125);
            this.label35.Name = "label35";
            this.label35.Size = new System.Drawing.Size(35, 12);
            this.label35.TabIndex = 0;
            this.label35.Text = "加深:";
            // 
            // label37
            // 
            this.label37.AutoSize = true;
            this.label37.Location = new System.Drawing.Point(146, 152);
            this.label37.Name = "label37";
            this.label37.Size = new System.Drawing.Size(35, 12);
            this.label37.TabIndex = 0;
            this.label37.Text = "吸魔:";
            // 
            // label36
            // 
            this.label36.AutoSize = true;
            this.label36.Location = new System.Drawing.Point(146, 125);
            this.label36.Name = "label36";
            this.label36.Size = new System.Drawing.Size(35, 12);
            this.label36.TabIndex = 0;
            this.label36.Text = "抵消:";
            // 
            // label34
            // 
            this.label34.AutoSize = true;
            this.label34.Location = new System.Drawing.Point(146, 98);
            this.label34.Name = "label34";
            this.label34.Size = new System.Drawing.Size(35, 12);
            this.label34.TabIndex = 0;
            this.label34.Text = "魔法:";
            // 
            // label33
            // 
            this.label33.AutoSize = true;
            this.label33.Location = new System.Drawing.Point(20, 98);
            this.label33.Name = "label33";
            this.label33.Size = new System.Drawing.Size(35, 12);
            this.label33.TabIndex = 0;
            this.label33.Text = "生命:";
            // 
            // label32
            // 
            this.label32.AutoSize = true;
            this.label32.Location = new System.Drawing.Point(146, 71);
            this.label32.Name = "label32";
            this.label32.Size = new System.Drawing.Size(35, 12);
            this.label32.TabIndex = 0;
            this.label32.Text = "闪避:";
            // 
            // label31
            // 
            this.label31.AutoSize = true;
            this.label31.Location = new System.Drawing.Point(20, 71);
            this.label31.Name = "label31";
            this.label31.Size = new System.Drawing.Size(35, 12);
            this.label31.TabIndex = 0;
            this.label31.Text = "速度:";
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(146, 44);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(35, 12);
            this.label30.TabIndex = 0;
            this.label30.Text = "防御:";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(20, 44);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(35, 12);
            this.label29.TabIndex = 0;
            this.label29.Text = "攻击:";
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Location = new System.Drawing.Point(146, 17);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(35, 12);
            this.label28.TabIndex = 0;
            this.label28.Text = "名字:";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Location = new System.Drawing.Point(20, 17);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(47, 12);
            this.label27.TabIndex = 0;
            this.label27.Text = "类型ID:";
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.tabControl2);
            this.tabPage1.Location = new System.Drawing.Point(4, 22);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Size = new System.Drawing.Size(309, 327);
            this.tabPage1.TabIndex = 4;
            this.tabPage1.Text = "地图配置";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // tabControl2
            // 
            this.tabControl2.Controls.Add(this.tabPage2);
            this.tabControl2.Controls.Add(this.tabPage3);
            this.tabControl2.Location = new System.Drawing.Point(3, 3);
            this.tabControl2.Name = "tabControl2";
            this.tabControl2.SelectedIndex = 0;
            this.tabControl2.Size = new System.Drawing.Size(281, 312);
            this.tabControl2.TabIndex = 0;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.textBox6);
            this.tabPage2.Controls.Add(this.label45);
            this.tabPage2.Controls.Add(this.checkBox2);
            this.tabPage2.Controls.Add(this.label44);
            this.tabPage2.Controls.Add(this.textBox5);
            this.tabPage2.Controls.Add(this.linkLabel5);
            this.tabPage2.Controls.Add(this.button5);
            this.tabPage2.Controls.Add(this.label17);
            this.tabPage2.Controls.Add(this.地图掉落最大数量);
            this.tabPage2.Controls.Add(this.label18);
            this.tabPage2.Controls.Add(this.地图掉落最小数量);
            this.tabPage2.Controls.Add(this.地图掉落列表);
            this.tabPage2.Controls.Add(this.label16);
            this.tabPage2.Controls.Add(this.label13);
            this.tabPage2.Controls.Add(this.地图最大元宝);
            this.tabPage2.Controls.Add(this.label15);
            this.tabPage2.Controls.Add(this.地图最小元宝);
            this.tabPage2.Controls.Add(this.label14);
            this.tabPage2.Controls.Add(this.地图最大金币);
            this.tabPage2.Controls.Add(this.label12);
            this.tabPage2.Controls.Add(this.地图最小金币);
            this.tabPage2.Controls.Add(this.label11);
            this.tabPage2.Controls.Add(this.地图序号);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(273, 286);
            this.tabPage2.TabIndex = 0;
            this.tabPage2.Text = "增加地图";
            this.tabPage2.UseVisualStyleBackColor = true;
            this.tabPage2.Click += new System.EventHandler(this.tabPage2_Click);
            // 
            // textBox6
            // 
            this.textBox6.Location = new System.Drawing.Point(209, 17);
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new System.Drawing.Size(41, 21);
            this.textBox6.TabIndex = 41;
            this.textBox6.Text = "600";
            // 
            // label45
            // 
            this.label45.AutoSize = true;
            this.label45.Location = new System.Drawing.Point(155, 20);
            this.label45.Name = "label45";
            this.label45.Size = new System.Drawing.Size(59, 12);
            this.label45.TabIndex = 42;
            this.label45.Text = "开启费用:";
            // 
            // checkBox2
            // 
            this.checkBox2.AutoSize = true;
            this.checkBox2.Location = new System.Drawing.Point(183, 158);
            this.checkBox2.Name = "checkBox2";
            this.checkBox2.Size = new System.Drawing.Size(48, 16);
            this.checkBox2.TabIndex = 40;
            this.checkBox2.Text = "副本";
            this.checkBox2.UseVisualStyleBackColor = true;
            // 
            // label44
            // 
            this.label44.AutoSize = true;
            this.label44.Location = new System.Drawing.Point(25, 161);
            this.label44.Name = "label44";
            this.label44.Size = new System.Drawing.Size(59, 12);
            this.label44.TabIndex = 39;
            this.label44.Text = "地图背景:";
            // 
            // textBox5
            // 
            this.textBox5.Location = new System.Drawing.Point(94, 156);
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new System.Drawing.Size(58, 21);
            this.textBox5.TabIndex = 38;
            this.textBox5.Text = "1.gif";
            // 
            // linkLabel5
            // 
            this.linkLabel5.AutoSize = true;
            this.linkLabel5.Location = new System.Drawing.Point(23, 124);
            this.linkLabel5.Name = "linkLabel5";
            this.linkLabel5.Size = new System.Drawing.Size(53, 12);
            this.linkLabel5.TabIndex = 37;
            this.linkLabel5.TabStop = true;
            this.linkLabel5.Text = "选择道具";
            this.linkLabel5.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel5_LinkClicked);
            // 
            // button5
            // 
            this.button5.Location = new System.Drawing.Point(23, 218);
            this.button5.Name = "button5";
            this.button5.Size = new System.Drawing.Size(227, 63);
            this.button5.TabIndex = 36;
            this.button5.Text = "增加地图配置";
            this.button5.UseVisualStyleBackColor = true;
            this.button5.Click += new System.EventHandler(this.button5_Click);
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Location = new System.Drawing.Point(158, 188);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(17, 12);
            this.label17.TabIndex = 35;
            this.label17.Text = "到";
            // 
            // 地图掉落最大数量
            // 
            this.地图掉落最大数量.Location = new System.Drawing.Point(182, 184);
            this.地图掉落最大数量.Name = "地图掉落最大数量";
            this.地图掉落最大数量.Size = new System.Drawing.Size(58, 21);
            this.地图掉落最大数量.TabIndex = 34;
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(25, 188);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(59, 12);
            this.label18.TabIndex = 33;
            this.label18.Text = "掉落数量:";
            // 
            // 地图掉落最小数量
            // 
            this.地图掉落最小数量.Location = new System.Drawing.Point(94, 184);
            this.地图掉落最小数量.Name = "地图掉落最小数量";
            this.地图掉落最小数量.Size = new System.Drawing.Size(58, 21);
            this.地图掉落最小数量.TabIndex = 32;
            // 
            // 地图掉落列表
            // 
            this.地图掉落列表.Location = new System.Drawing.Point(93, 99);
            this.地图掉落列表.Multiline = true;
            this.地图掉落列表.Name = "地图掉落列表";
            this.地图掉落列表.Size = new System.Drawing.Size(147, 51);
            this.地图掉落列表.TabIndex = 31;
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(23, 102);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(59, 12);
            this.label16.TabIndex = 30;
            this.label16.Text = "掉落道具:";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(158, 76);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(17, 12);
            this.label13.TabIndex = 29;
            this.label13.Text = "到";
            // 
            // 地图最大元宝
            // 
            this.地图最大元宝.Location = new System.Drawing.Point(182, 72);
            this.地图最大元宝.Name = "地图最大元宝";
            this.地图最大元宝.Size = new System.Drawing.Size(58, 21);
            this.地图最大元宝.TabIndex = 28;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(49, 77);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(35, 12);
            this.label15.TabIndex = 27;
            this.label15.Text = "元宝:";
            // 
            // 地图最小元宝
            // 
            this.地图最小元宝.Location = new System.Drawing.Point(94, 72);
            this.地图最小元宝.Name = "地图最小元宝";
            this.地图最小元宝.Size = new System.Drawing.Size(58, 21);
            this.地图最小元宝.TabIndex = 26;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(158, 48);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(17, 12);
            this.label14.TabIndex = 25;
            this.label14.Text = "到";
            // 
            // 地图最大金币
            // 
            this.地图最大金币.Location = new System.Drawing.Point(182, 44);
            this.地图最大金币.Name = "地图最大金币";
            this.地图最大金币.Size = new System.Drawing.Size(58, 21);
            this.地图最大金币.TabIndex = 24;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(49, 49);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(35, 12);
            this.label12.TabIndex = 21;
            this.label12.Text = "金币:";
            // 
            // 地图最小金币
            // 
            this.地图最小金币.Location = new System.Drawing.Point(94, 44);
            this.地图最小金币.Name = "地图最小金币";
            this.地图最小金币.Size = new System.Drawing.Size(58, 21);
            this.地图最小金币.TabIndex = 20;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(24, 22);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(59, 12);
            this.label11.TabIndex = 19;
            this.label11.Text = "地图序号:";
            // 
            // 地图序号
            // 
            this.地图序号.Location = new System.Drawing.Point(93, 17);
            this.地图序号.Name = "地图序号";
            this.地图序号.Size = new System.Drawing.Size(58, 21);
            this.地图序号.TabIndex = 18;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.button6);
            this.tabPage3.Controls.Add(this.linkLabel9);
            this.tabPage3.Controls.Add(this.怪物经验);
            this.tabPage3.Controls.Add(this.label25);
            this.tabPage3.Controls.Add(this.button8);
            this.tabPage3.Controls.Add(this.linkLabel7);
            this.tabPage3.Controls.Add(this.linkLabel8);
            this.tabPage3.Controls.Add(this.button7);
            this.tabPage3.Controls.Add(this.怪物掉落);
            this.tabPage3.Controls.Add(this.label24);
            this.tabPage3.Controls.Add(this.怪物成长);
            this.tabPage3.Controls.Add(this.怪物最小等级);
            this.tabPage3.Controls.Add(this.怪物序号);
            this.tabPage3.Controls.Add(this.怪物最大掉落);
            this.tabPage3.Controls.Add(this.label23);
            this.tabPage3.Controls.Add(this.linkLabel6);
            this.tabPage3.Controls.Add(this.label22);
            this.tabPage3.Controls.Add(this.label20);
            this.tabPage3.Controls.Add(this.怪物最大等级);
            this.tabPage3.Controls.Add(this.label21);
            this.tabPage3.Controls.Add(this.label19);
            this.tabPage3.Location = new System.Drawing.Point(4, 22);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage3.Size = new System.Drawing.Size(273, 286);
            this.tabPage3.TabIndex = 1;
            this.tabPage3.Text = "增加怪物";
            this.tabPage3.UseVisualStyleBackColor = true;
            this.tabPage3.Click += new System.EventHandler(this.tabPage3_Click);
            // 
            // button6
            // 
            this.button6.Location = new System.Drawing.Point(2, 0);
            this.button6.Name = "button6";
            this.button6.Size = new System.Drawing.Size(272, 283);
            this.button6.TabIndex = 51;
            this.button6.Text = "创建";
            this.button6.UseVisualStyleBackColor = true;
            this.button6.Click += new System.EventHandler(this.button6_Click);
            // 
            // linkLabel9
            // 
            this.linkLabel9.AutoSize = true;
            this.linkLabel9.Location = new System.Drawing.Point(7, 109);
            this.linkLabel9.Name = "linkLabel9";
            this.linkLabel9.Size = new System.Drawing.Size(53, 12);
            this.linkLabel9.TabIndex = 50;
            this.linkLabel9.TabStop = true;
            this.linkLabel9.Text = "选择道具";
            this.linkLabel9.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel9_LinkClicked);
            // 
            // 怪物经验
            // 
            this.怪物经验.Location = new System.Drawing.Point(44, 63);
            this.怪物经验.Name = "怪物经验";
            this.怪物经验.Size = new System.Drawing.Size(58, 21);
            this.怪物经验.TabIndex = 48;
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Location = new System.Drawing.Point(9, 67);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(35, 12);
            this.label25.TabIndex = 49;
            this.label25.Text = "经验:";
            // 
            // button8
            // 
            this.button8.Location = new System.Drawing.Point(54, 178);
            this.button8.Name = "button8";
            this.button8.Size = new System.Drawing.Size(148, 40);
            this.button8.TabIndex = 47;
            this.button8.Text = "重置";
            this.button8.UseVisualStyleBackColor = true;
            this.button8.Click += new System.EventHandler(this.button8_Click);
            // 
            // linkLabel7
            // 
            this.linkLabel7.AutoSize = true;
            this.linkLabel7.Location = new System.Drawing.Point(31, 149);
            this.linkLabel7.Name = "linkLabel7";
            this.linkLabel7.Size = new System.Drawing.Size(53, 12);
            this.linkLabel7.TabIndex = 46;
            this.linkLabel7.TabStop = true;
            this.linkLabel7.Text = "加入列表";
            this.linkLabel7.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel7_LinkClicked);
            // 
            // linkLabel8
            // 
            this.linkLabel8.AutoSize = true;
            this.linkLabel8.Location = new System.Drawing.Point(146, 149);
            this.linkLabel8.Name = "linkLabel8";
            this.linkLabel8.Size = new System.Drawing.Size(101, 12);
            this.linkLabel8.TabIndex = 46;
            this.linkLabel8.TabStop = true;
            this.linkLabel8.Text = "查看已加入的怪物";
            this.linkLabel8.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel8_LinkClicked);
            // 
            // button7
            // 
            this.button7.Location = new System.Drawing.Point(22, 224);
            this.button7.Name = "button7";
            this.button7.Size = new System.Drawing.Size(233, 49);
            this.button7.TabIndex = 44;
            this.button7.Text = "保存配置";
            this.button7.UseVisualStyleBackColor = true;
            this.button7.Click += new System.EventHandler(this.button7_Click);
            // 
            // 怪物掉落
            // 
            this.怪物掉落.Location = new System.Drawing.Point(68, 90);
            this.怪物掉落.Multiline = true;
            this.怪物掉落.Name = "怪物掉落";
            this.怪物掉落.Size = new System.Drawing.Size(189, 47);
            this.怪物掉落.TabIndex = 42;
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Location = new System.Drawing.Point(9, 90);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(59, 12);
            this.label24.TabIndex = 41;
            this.label24.Text = "掉落道具:";
            // 
            // 怪物成长
            // 
            this.怪物成长.Location = new System.Drawing.Point(199, 10);
            this.怪物成长.Name = "怪物成长";
            this.怪物成长.Size = new System.Drawing.Size(58, 21);
            this.怪物成长.TabIndex = 30;
            // 
            // 怪物最小等级
            // 
            this.怪物最小等级.Location = new System.Drawing.Point(45, 37);
            this.怪物最小等级.Name = "怪物最小等级";
            this.怪物最小等级.Size = new System.Drawing.Size(32, 21);
            this.怪物最小等级.TabIndex = 26;
            // 
            // 怪物序号
            // 
            this.怪物序号.Location = new System.Drawing.Point(45, 10);
            this.怪物序号.Name = "怪物序号";
            this.怪物序号.Size = new System.Drawing.Size(58, 21);
            this.怪物序号.TabIndex = 20;
            this.怪物序号.TextChanged += new System.EventHandler(this.怪物序号_TextChanged);
            // 
            // 怪物最大掉落
            // 
            this.怪物最大掉落.Location = new System.Drawing.Point(199, 37);
            this.怪物最大掉落.Name = "怪物最大掉落";
            this.怪物最大掉落.Size = new System.Drawing.Size(58, 21);
            this.怪物最大掉落.TabIndex = 39;
            this.怪物最大掉落.Text = "0";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(138, 41);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(59, 12);
            this.label23.TabIndex = 40;
            this.label23.Text = "最大掉落:";
            // 
            // linkLabel6
            // 
            this.linkLabel6.AutoSize = true;
            this.linkLabel6.Location = new System.Drawing.Point(115, 14);
            this.linkLabel6.Name = "linkLabel6";
            this.linkLabel6.Size = new System.Drawing.Size(29, 12);
            this.linkLabel6.TabIndex = 38;
            this.linkLabel6.TabStop = true;
            this.linkLabel6.Text = "选择";
            this.linkLabel6.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel6_LinkClicked);
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(164, 14);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(35, 12);
            this.label22.TabIndex = 31;
            this.label22.Text = "成长:";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(79, 41);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(17, 12);
            this.label20.TabIndex = 29;
            this.label20.Text = "到";
            // 
            // 怪物最大等级
            // 
            this.怪物最大等级.Location = new System.Drawing.Point(100, 37);
            this.怪物最大等级.Name = "怪物最大等级";
            this.怪物最大等级.Size = new System.Drawing.Size(32, 21);
            this.怪物最大等级.TabIndex = 28;
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(9, 42);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(35, 12);
            this.label21.TabIndex = 27;
            this.label21.Text = "等级:";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(10, 14);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(35, 12);
            this.label19.TabIndex = 21;
            this.label19.Text = "序号:";
            // 
            // 宠物存档
            // 
            this.宠物存档.Controls.Add(this.pictureBox1);
            this.宠物存档.Controls.Add(this.button4);
            this.宠物存档.Controls.Add(this.linkLabel4);
            this.宠物存档.Controls.Add(this.label9);
            this.宠物存档.Controls.Add(this.宠物成长);
            this.宠物存档.Controls.Add(this.宠物序号);
            this.宠物存档.Controls.Add(this.label10);
            this.宠物存档.Location = new System.Drawing.Point(4, 22);
            this.宠物存档.Name = "宠物存档";
            this.宠物存档.Size = new System.Drawing.Size(309, 327);
            this.宠物存档.TabIndex = 3;
            this.宠物存档.Text = "宠物存档";
            this.宠物存档.UseVisualStyleBackColor = true;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Location = new System.Drawing.Point(26, 147);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(240, 156);
            this.pictureBox1.TabIndex = 20;
            this.pictureBox1.TabStop = false;
            // 
            // button4
            // 
            this.button4.Location = new System.Drawing.Point(44, 104);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(194, 35);
            this.button4.TabIndex = 19;
            this.button4.Text = "添加宠物";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // linkLabel4
            // 
            this.linkLabel4.AutoSize = true;
            this.linkLabel4.Location = new System.Drawing.Point(223, 39);
            this.linkLabel4.Name = "linkLabel4";
            this.linkLabel4.Size = new System.Drawing.Size(53, 12);
            this.linkLabel4.TabIndex = 18;
            this.linkLabel4.TabStop = true;
            this.linkLabel4.Text = "选择宠物";
            this.linkLabel4.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel4_LinkClicked);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(12, 75);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(59, 12);
            this.label9.TabIndex = 17;
            this.label9.Text = "宠物成长:";
            // 
            // 宠物成长
            // 
            this.宠物成长.Location = new System.Drawing.Point(81, 70);
            this.宠物成长.Name = "宠物成长";
            this.宠物成长.Size = new System.Drawing.Size(134, 21);
            this.宠物成长.TabIndex = 16;
            this.宠物成长.TextChanged += new System.EventHandler(this.宠物成长_TextChanged);
            // 
            // 宠物序号
            // 
            this.宠物序号.Location = new System.Drawing.Point(81, 34);
            this.宠物序号.Name = "宠物序号";
            this.宠物序号.Size = new System.Drawing.Size(134, 21);
            this.宠物序号.TabIndex = 14;
            this.宠物序号.Leave += new System.EventHandler(this.宠物序号_Leave);
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(12, 39);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(59, 12);
            this.label10.TabIndex = 15;
            this.label10.Text = "宠物序号:";
            // 
            // 道具存档
            // 
            this.道具存档.Controls.Add(this.button3);
            this.道具存档.Controls.Add(this.linkLabel3);
            this.道具存档.Controls.Add(this.label8);
            this.道具存档.Controls.Add(this.添加道具数量);
            this.道具存档.Controls.Add(this.道具序号2);
            this.道具存档.Controls.Add(this.label7);
            this.道具存档.Location = new System.Drawing.Point(4, 22);
            this.道具存档.Name = "道具存档";
            this.道具存档.Size = new System.Drawing.Size(309, 327);
            this.道具存档.TabIndex = 2;
            this.道具存档.Text = "道具存档";
            this.道具存档.UseVisualStyleBackColor = true;
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(44, 107);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(194, 35);
            this.button3.TabIndex = 13;
            this.button3.Text = "添加道具";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // linkLabel3
            // 
            this.linkLabel3.AutoSize = true;
            this.linkLabel3.Location = new System.Drawing.Point(223, 28);
            this.linkLabel3.Name = "linkLabel3";
            this.linkLabel3.Size = new System.Drawing.Size(53, 12);
            this.linkLabel3.TabIndex = 12;
            this.linkLabel3.TabStop = true;
            this.linkLabel3.Text = "选择道具";
            this.linkLabel3.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel3_LinkClicked);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(12, 64);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(59, 12);
            this.label8.TabIndex = 9;
            this.label8.Text = "添加数量:";
            // 
            // 添加道具数量
            // 
            this.添加道具数量.Location = new System.Drawing.Point(81, 59);
            this.添加道具数量.Name = "添加道具数量";
            this.添加道具数量.Size = new System.Drawing.Size(134, 21);
            this.添加道具数量.TabIndex = 8;
            // 
            // 道具序号2
            // 
            this.道具序号2.Location = new System.Drawing.Point(81, 23);
            this.道具序号2.Name = "道具序号2";
            this.道具序号2.Size = new System.Drawing.Size(134, 21);
            this.道具序号2.TabIndex = 6;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(12, 28);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(59, 12);
            this.label7.TabIndex = 7;
            this.label7.Text = "道具序号:";
            // 
            // 道具配置
            // 
            this.道具配置.Controls.Add(this.linkLabel14);
            this.道具配置.Controls.Add(this.linkLabel13);
            this.道具配置.Controls.Add(this.label26);
            this.道具配置.Controls.Add(this.出售价格);
            this.道具配置.Controls.Add(this.道具说明);
            this.道具配置.Controls.Add(this.道具脚本);
            this.道具配置.Controls.Add(this.道具序号);
            this.道具配置.Controls.Add(this.道具图标);
            this.道具配置.Controls.Add(this.道具名字);
            this.道具配置.Controls.Add(this.linkLabel11);
            this.道具配置.Controls.Add(this.linkLabel10);
            this.道具配置.Controls.Add(this.linkLabel2);
            this.道具配置.Controls.Add(this.linkLabel1);
            this.道具配置.Controls.Add(this.label6);
            this.道具配置.Controls.Add(this.button2);
            this.道具配置.Controls.Add(this.label5);
            this.道具配置.Controls.Add(this.label4);
            this.道具配置.Controls.Add(this.label3);
            this.道具配置.Controls.Add(this.label2);
            this.道具配置.Location = new System.Drawing.Point(4, 22);
            this.道具配置.Name = "道具配置";
            this.道具配置.Size = new System.Drawing.Size(309, 327);
            this.道具配置.TabIndex = 0;
            this.道具配置.Text = "道具配置";
            this.道具配置.UseVisualStyleBackColor = true;
            this.道具配置.Click += new System.EventHandler(this.道具配置_Click);
            // 
            // linkLabel14
            // 
            this.linkLabel14.AutoSize = true;
            this.linkLabel14.Location = new System.Drawing.Point(38, 256);
            this.linkLabel14.Name = "linkLabel14";
            this.linkLabel14.Size = new System.Drawing.Size(53, 12);
            this.linkLabel14.TabIndex = 44;
            this.linkLabel14.TabStop = true;
            this.linkLabel14.Text = "选择装备";
            this.linkLabel14.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel14_LinkClicked);
            // 
            // linkLabel13
            // 
            this.linkLabel13.AutoSize = true;
            this.linkLabel13.Location = new System.Drawing.Point(38, 171);
            this.linkLabel13.Name = "linkLabel13";
            this.linkLabel13.Size = new System.Drawing.Size(53, 12);
            this.linkLabel13.TabIndex = 43;
            this.linkLabel13.TabStop = true;
            this.linkLabel13.Text = "选择装备";
            this.linkLabel13.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel13_LinkClicked);
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(38, 92);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(59, 12);
            this.label26.TabIndex = 42;
            this.label26.Text = "出售价格:";
            // 
            // 出售价格
            // 
            this.出售价格.Location = new System.Drawing.Point(108, 87);
            this.出售价格.Name = "出售价格";
            this.出售价格.Size = new System.Drawing.Size(134, 21);
            this.出售价格.TabIndex = 41;
            // 
            // 道具说明
            // 
            this.道具说明.Location = new System.Drawing.Point(108, 196);
            this.道具说明.Multiline = true;
            this.道具说明.Name = "道具说明";
            this.道具说明.Size = new System.Drawing.Size(162, 73);
            this.道具说明.TabIndex = 9;
            // 
            // 道具脚本
            // 
            this.道具脚本.Location = new System.Drawing.Point(108, 116);
            this.道具脚本.Multiline = true;
            this.道具脚本.Name = "道具脚本";
            this.道具脚本.Size = new System.Drawing.Size(162, 73);
            this.道具脚本.TabIndex = 6;
            // 
            // 道具序号
            // 
            this.道具序号.Location = new System.Drawing.Point(108, 64);
            this.道具序号.Name = "道具序号";
            this.道具序号.Size = new System.Drawing.Size(134, 21);
            this.道具序号.TabIndex = 4;
            // 
            // 道具图标
            // 
            this.道具图标.Location = new System.Drawing.Point(108, 39);
            this.道具图标.Name = "道具图标";
            this.道具图标.Size = new System.Drawing.Size(134, 21);
            this.道具图标.TabIndex = 2;
            // 
            // 道具名字
            // 
            this.道具名字.Location = new System.Drawing.Point(108, 12);
            this.道具名字.Name = "道具名字";
            this.道具名字.Size = new System.Drawing.Size(134, 21);
            this.道具名字.TabIndex = 0;
            // 
            // linkLabel11
            // 
            this.linkLabel11.AutoSize = true;
            this.linkLabel11.Location = new System.Drawing.Point(38, 239);
            this.linkLabel11.Name = "linkLabel11";
            this.linkLabel11.Size = new System.Drawing.Size(53, 12);
            this.linkLabel11.TabIndex = 40;
            this.linkLabel11.TabStop = true;
            this.linkLabel11.Text = "选择宠物";
            this.linkLabel11.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel11_LinkClicked);
            // 
            // linkLabel10
            // 
            this.linkLabel10.AutoSize = true;
            this.linkLabel10.Location = new System.Drawing.Point(38, 154);
            this.linkLabel10.Name = "linkLabel10";
            this.linkLabel10.Size = new System.Drawing.Size(53, 12);
            this.linkLabel10.TabIndex = 39;
            this.linkLabel10.TabStop = true;
            this.linkLabel10.Text = "选择宠物";
            this.linkLabel10.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel10_LinkClicked);
            // 
            // linkLabel2
            // 
            this.linkLabel2.AutoSize = true;
            this.linkLabel2.Location = new System.Drawing.Point(38, 220);
            this.linkLabel2.Name = "linkLabel2";
            this.linkLabel2.Size = new System.Drawing.Size(53, 12);
            this.linkLabel2.TabIndex = 12;
            this.linkLabel2.TabStop = true;
            this.linkLabel2.Text = "选择道具";
            this.linkLabel2.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel2_LinkClicked);
            // 
            // linkLabel1
            // 
            this.linkLabel1.AutoSize = true;
            this.linkLabel1.Location = new System.Drawing.Point(38, 135);
            this.linkLabel1.Name = "linkLabel1";
            this.linkLabel1.Size = new System.Drawing.Size(53, 12);
            this.linkLabel1.TabIndex = 11;
            this.linkLabel1.TabStop = true;
            this.linkLabel1.Text = "选择道具";
            this.linkLabel1.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.linkLabel1_LinkClicked);
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(38, 197);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(59, 12);
            this.label6.TabIndex = 10;
            this.label6.Text = "道具说明:";
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(44, 280);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(194, 35);
            this.button2.TabIndex = 8;
            this.button2.Text = "添加道具";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(38, 117);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(59, 12);
            this.label5.TabIndex = 7;
            this.label5.Text = "道具脚本:";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(38, 69);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(59, 12);
            this.label4.TabIndex = 5;
            this.label4.Text = "道具序号:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(38, 44);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(59, 12);
            this.label3.TabIndex = 3;
            this.label3.Text = "道具图标:";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(38, 17);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(59, 12);
            this.label2.TabIndex = 1;
            this.label2.Text = "道具名字:";
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage7);
            this.tabControl1.Controls.Add(this.道具配置);
            this.tabControl1.Controls.Add(this.道具存档);
            this.tabControl1.Controls.Add(this.宠物存档);
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage4);
            this.tabControl1.Controls.Add(this.tabPage5);
            this.tabControl1.Controls.Add(this.tabPage6);
            this.tabControl1.Controls.Add(this.tabPage8);
            this.tabControl1.Controls.Add(this.tabPage9);
            this.tabControl1.Location = new System.Drawing.Point(500, 42);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(317, 353);
            this.tabControl1.TabIndex = 6;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.tabControl1_SelectedIndexChanged);
            this.tabControl1.Selected += new System.Windows.Forms.TabControlEventHandler(this.tabControl1_Selected);
            this.tabControl1.TabIndexChanged += new System.EventHandler(this.tabControl1_TabIndexChanged);
            // 
            // tabPage7
            // 
            this.tabPage7.Controls.Add(this.button16);
            this.tabPage7.Controls.Add(this.button15);
            this.tabPage7.Controls.Add(this.button14);
            this.tabPage7.Location = new System.Drawing.Point(4, 22);
            this.tabPage7.Name = "tabPage7";
            this.tabPage7.Size = new System.Drawing.Size(309, 327);
            this.tabPage7.TabIndex = 8;
            this.tabPage7.Text = "其他功能";
            this.tabPage7.UseVisualStyleBackColor = true;
            // 
            // button16
            // 
            this.button16.Location = new System.Drawing.Point(80, 170);
            this.button16.Name = "button16";
            this.button16.Size = new System.Drawing.Size(124, 43);
            this.button16.TabIndex = 0;
            this.button16.Text = "增加套装";
            this.button16.UseVisualStyleBackColor = true;
            this.button16.Click += new System.EventHandler(this.button16_Click);
            // 
            // button15
            // 
            this.button15.Location = new System.Drawing.Point(80, 121);
            this.button15.Name = "button15";
            this.button15.Size = new System.Drawing.Size(124, 43);
            this.button15.TabIndex = 0;
            this.button15.Text = "管理任务";
            this.button15.UseVisualStyleBackColor = true;
            this.button15.Click += new System.EventHandler(this.button15_Click);
            // 
            // button14
            // 
            this.button14.Location = new System.Drawing.Point(80, 72);
            this.button14.Name = "button14";
            this.button14.Size = new System.Drawing.Size(124, 43);
            this.button14.TabIndex = 0;
            this.button14.Text = "管理宠物进化路线";
            this.button14.UseVisualStyleBackColor = true;
            this.button14.Click += new System.EventHandler(this.button14_Click);
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.textBox2);
            this.tabPage4.Controls.Add(this.button10);
            this.tabPage4.Location = new System.Drawing.Point(4, 22);
            this.tabPage4.Margin = new System.Windows.Forms.Padding(2);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Padding = new System.Windows.Forms.Padding(2);
            this.tabPage4.Size = new System.Drawing.Size(309, 327);
            this.tabPage4.TabIndex = 5;
            this.tabPage4.Text = "哈希验证";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // textBox2
            // 
            this.textBox2.AllowDrop = true;
            this.textBox2.Location = new System.Drawing.Point(15, 80);
            this.textBox2.MaxLength = 99999999;
            this.textBox2.Multiline = true;
            this.textBox2.Name = "textBox2";
            this.textBox2.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBox2.Size = new System.Drawing.Size(259, 226);
            this.textBox2.TabIndex = 8;
            // 
            // button10
            // 
            this.button10.Location = new System.Drawing.Point(89, 23);
            this.button10.Margin = new System.Windows.Forms.Padding(2);
            this.button10.Name = "button10";
            this.button10.Size = new System.Drawing.Size(93, 35);
            this.button10.TabIndex = 0;
            this.button10.Text = "生成哈希配置";
            this.button10.UseVisualStyleBackColor = true;
            this.button10.Click += new System.EventHandler(this.button10_Click);
            // 
            // tabPage9
            // 
            this.tabPage9.Location = new System.Drawing.Point(4, 22);
            this.tabPage9.Name = "tabPage9";
            this.tabPage9.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage9.Size = new System.Drawing.Size(309, 327);
            this.tabPage9.TabIndex = 10;
            this.tabPage9.Text = "掉落测试";
            this.tabPage9.UseVisualStyleBackColor = true;
            // 
            // button18
            // 
            this.button18.Location = new System.Drawing.Point(185, 331);
            this.button18.Name = "button18";
            this.button18.Size = new System.Drawing.Size(82, 48);
            this.button18.TabIndex = 7;
            this.button18.Text = "将存档使用新密钥保存";
            this.button18.UseVisualStyleBackColor = true;
            this.button18.Click += new System.EventHandler(this.button18_Click);
            // 
            // checkBox1
            // 
            this.checkBox1.AutoSize = true;
            this.checkBox1.Checked = true;
            this.checkBox1.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox1.Location = new System.Drawing.Point(336, 387);
            this.checkBox1.Name = "checkBox1";
            this.checkBox1.Size = new System.Drawing.Size(96, 16);
            this.checkBox1.TabIndex = 8;
            this.checkBox1.Text = "使用旧版算法";
            this.checkBox1.UseVisualStyleBackColor = true;
            this.checkBox1.CheckedChanged += new System.EventHandler(this.checkBox1_CheckedChanged);
            // 
            // button11
            // 
            this.button11.Location = new System.Drawing.Point(273, 331);
            this.button11.Name = "button11";
            this.button11.Size = new System.Drawing.Size(99, 48);
            this.button11.TabIndex = 9;
            this.button11.Text = "批量加入新道具";
            this.button11.UseVisualStyleBackColor = true;
            this.button11.Click += new System.EventHandler(this.button11_Click_1);
            // 
            // 管理
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.ClientSize = new System.Drawing.Size(850, 407);
            this.Controls.Add(this.button11);
            this.Controls.Add(this.checkBox1);
            this.Controls.Add(this.button18);
            this.Controls.Add(this.button9);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.文件);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.地图ID);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.comboBox1);
            this.Name = "管理";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "配置编辑器";
            this.Load += new System.EventHandler(this.Form1_Load);
            this.tabPage8.ResumeLayout(false);
            this.tabPage6.ResumeLayout(false);
            this.tabPage6.PerformLayout();
            this.tabPage5.ResumeLayout(false);
            this.tabPage5.PerformLayout();
            this.tabPage1.ResumeLayout(false);
            this.tabControl2.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage2.PerformLayout();
            this.tabPage3.ResumeLayout(false);
            this.tabPage3.PerformLayout();
            this.宠物存档.ResumeLayout(false);
            this.宠物存档.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.道具存档.ResumeLayout(false);
            this.道具存档.PerformLayout();
            this.道具配置.ResumeLayout(false);
            this.道具配置.PerformLayout();
            this.tabControl1.ResumeLayout(false);
            this.tabPage7.ResumeLayout(false);
            this.tabPage4.ResumeLayout(false);
            this.tabPage4.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ComboBox comboBox1;
        private System.Windows.Forms.TextBox textBox1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox 地图ID;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.TextBox 文件;
        private System.Windows.Forms.Button button9;
        private System.Windows.Forms.TabPage tabPage8;
        private System.Windows.Forms.Button button17;
        private System.Windows.Forms.TabPage tabPage6;
        private System.Windows.Forms.Button button13;
        private System.Windows.Forms.LinkLabel linkLabel12;
        private System.Windows.Forms.Label label41;
        private System.Windows.Forms.TextBox 存档装备ID;
        private System.Windows.Forms.TabPage tabPage5;
        private System.Windows.Forms.TextBox textBox4;
        private System.Windows.Forms.TextBox 装备命中;
        private System.Windows.Forms.TextBox 装备图标;
        private System.Windows.Forms.TextBox 装备吸血;
        private System.Windows.Forms.TextBox 装备加深;
        private System.Windows.Forms.TextBox 装备吸魔;
        private System.Windows.Forms.TextBox 装备抵消;
        private System.Windows.Forms.TextBox 装备魔法;
        private System.Windows.Forms.TextBox 装备生命;
        private System.Windows.Forms.TextBox 装备闪避;
        private System.Windows.Forms.TextBox 装备速度;
        private System.Windows.Forms.TextBox 装备防御;
        private System.Windows.Forms.TextBox 装备攻击;
        private System.Windows.Forms.TextBox 装备名字;
        private System.Windows.Forms.TextBox 装备_ID;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.Button button12;
        private System.Windows.Forms.ComboBox 主属性;
        private System.Windows.Forms.Label label46;
        private System.Windows.Forms.ComboBox 装备类型;
        private System.Windows.Forms.Label label40;
        private System.Windows.Forms.Label label43;
        private System.Windows.Forms.Label label39;
        private System.Windows.Forms.Label label38;
        private System.Windows.Forms.Label label35;
        private System.Windows.Forms.Label label37;
        private System.Windows.Forms.Label label36;
        private System.Windows.Forms.Label label34;
        private System.Windows.Forms.Label label33;
        private System.Windows.Forms.Label label32;
        private System.Windows.Forms.Label label31;
        private System.Windows.Forms.Label label30;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabControl tabControl2;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.TextBox textBox6;
        private System.Windows.Forms.Label label45;
        private System.Windows.Forms.CheckBox checkBox2;
        private System.Windows.Forms.Label label44;
        private System.Windows.Forms.TextBox textBox5;
        private System.Windows.Forms.LinkLabel linkLabel5;
        private System.Windows.Forms.Button button5;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.TextBox 地图掉落最大数量;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.TextBox 地图掉落最小数量;
        private System.Windows.Forms.TextBox 地图掉落列表;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox 地图最大元宝;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox 地图最小元宝;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox 地图最大金币;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox 地图最小金币;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.TextBox 地图序号;
        private System.Windows.Forms.TabPage tabPage3;
        private System.Windows.Forms.Button button6;
        private System.Windows.Forms.LinkLabel linkLabel9;
        private System.Windows.Forms.TextBox 怪物经验;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.Button button8;
        private System.Windows.Forms.LinkLabel linkLabel7;
        private System.Windows.Forms.LinkLabel linkLabel8;
        private System.Windows.Forms.Button button7;
        private System.Windows.Forms.TextBox 怪物掉落;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.TextBox 怪物成长;
        private System.Windows.Forms.TextBox 怪物最小等级;
        private System.Windows.Forms.TextBox 怪物序号;
        private System.Windows.Forms.TextBox 怪物最大掉落;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.LinkLabel linkLabel6;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.TextBox 怪物最大等级;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.TabPage 宠物存档;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.Button button4;
        private System.Windows.Forms.LinkLabel linkLabel4;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.TextBox 宠物成长;
        private System.Windows.Forms.TextBox 宠物序号;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.TabPage 道具存档;
        private System.Windows.Forms.Button button3;
        private System.Windows.Forms.LinkLabel linkLabel3;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.TextBox 添加道具数量;
        private System.Windows.Forms.TextBox 道具序号2;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.TabPage 道具配置;
        private System.Windows.Forms.LinkLabel linkLabel14;
        private System.Windows.Forms.LinkLabel linkLabel13;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.TextBox 出售价格;
        private System.Windows.Forms.TextBox 道具说明;
        public System.Windows.Forms.TextBox 道具脚本;
        private System.Windows.Forms.TextBox 道具序号;
        private System.Windows.Forms.TextBox 道具图标;
        private System.Windows.Forms.TextBox 道具名字;
        private System.Windows.Forms.LinkLabel linkLabel11;
        private System.Windows.Forms.LinkLabel linkLabel10;
        private System.Windows.Forms.LinkLabel linkLabel2;
        private System.Windows.Forms.LinkLabel linkLabel1;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage7;
        private System.Windows.Forms.Button button16;
        private System.Windows.Forms.Button button15;
        private System.Windows.Forms.Button button14;
        private System.Windows.Forms.TabPage tabPage4;
        private System.Windows.Forms.TextBox textBox2;
        private System.Windows.Forms.Button button10;
        private System.Windows.Forms.TextBox 装备特效;
        private System.Windows.Forms.Label label47;
        private System.Windows.Forms.TabPage tabPage9;
        private System.Windows.Forms.Button button18;
        private System.Windows.Forms.CheckBox checkBox1;
        private System.Windows.Forms.Button button11;
    }
}

