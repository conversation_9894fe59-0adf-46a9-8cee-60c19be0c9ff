﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using PetShikong;
using System.IO;
using Newtonsoft.Json;

namespace Admin
{
    public partial class 装备管理 : Form
    {
        public 装备管理()
        {
            InitializeComponent();
        }

        private void 装备管理_Load(object sender, EventArgs e)
        {

        }

        List<装备类型> 所有装备 = new 数据处理().取装备记录表();
        //string 装备使用类型;
        装备类型 现编辑装备= new 装备类型();
        List<suits> 所有套装 = new 数据处理().取所有套装();
        数据处理 处理 = new 数据处理();
        private void button1_Click(object sender, EventArgs e)
        {
            List<装备类型> 所有装备 = new 数据处理().取装备记录表();
            dataGridView1.Rows.Clear();
            MessageBox.Show(所有装备.Count.ToString());
            foreach (装备类型 装备 in 所有装备)
            {
                try
                {
                    string[] str = { 装备.ID, 装备.名字, 取套装名称(装备.suitID) };
                    dataGridView1.Rows.Add(str);
                }
                catch(Exception ex) {
                    MessageBox.Show(ex.Message);
                }
            }
        }

        private void dataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            string typeID= dataGridView1.Rows[dataGridView1.CurrentRow.Index].Cells["num"].Value.ToString();
            现编辑装备 = new 装备类型();
            foreach (装备类型 装备 in 所有装备)
            {
                if (装备.ID.Equals(typeID))
                {
                    现编辑装备 = 装备;
                    break;
                }
            }
            //现编辑装备 = 所有装备[dataGridView1.CurrentRow.Index];
            装备_ID.Text = 现编辑装备.ID;
            装备名字.Text = 现编辑装备.名字;
            装备加深.Text = 现编辑装备.加深;
            装备吸血.Text = 现编辑装备.吸血;
            装备吸魔.Text = 现编辑装备.吸魔;
            装备图标.Text = 现编辑装备.ICO;
            装备抵消.Text = 现编辑装备.抵消;
            装备攻击.Text = 现编辑装备.攻击;
            装备生命.Text = 现编辑装备.生命;
            装备类型.Text = 现编辑装备.类型;
            装备速度.Text = 现编辑装备.速度;
            装备闪避.Text = 现编辑装备.闪避;
            装备防御.Text = 现编辑装备.防御;
            装备魔法.Text = 现编辑装备.魔法;
            装备命中.Text = 现编辑装备.命中;
            装备套装.Text = 现编辑装备.suitID;
            主属性.Text = 现编辑装备.主属性;
            textBox1.Text = 数据处理.装备定义配置路径 + 数据处理.GetStringHash(RC4.EncryptRC4wq(现编辑装备.ID, 数据处理.获取密钥(2))) + ".dat";

        }

        private string 取套装名称(string 套装ID)
        {
            if (套装ID==null) return "";
            foreach (suits 套装 in 所有套装)
            {
                if (套装.套装序号.Equals(套装ID))
                {
                    return 套装.套装名;
                }
            }
            return "";


        }

        private void button8_Click(object sender, EventArgs e)
        {
            if (textBox2.Text != "")
            {
                dataGridView1.Rows.Clear();
                if (所有装备.Count < 1)
                    所有装备 = new 数据处理().取装备记录表();

                foreach (装备类型 装备 in 所有装备)
                {
                    if (装备.ID.IndexOf(textBox2.Text)>-1 || 装备.名字.IndexOf(textBox2.Text) > -1)
                    {
                        string[] str = { 装备.ID, 装备.名字, 取套装名称(装备.suitID) };
                        dataGridView1.Rows.Add(str);
                    }
                }
            }
        }

        private void button12_Click(object sender, EventArgs e)
        {

            装备类型 装备 = 现编辑装备;
            装备.ID = 装备_ID.Text;
            装备.抵消 = 装备抵消.Text;
            装备.防御 = 装备防御.Text;
            装备.攻击 = 装备攻击.Text;
            装备.加深 = 装备加深.Text;
            装备.类型 = 装备类型.Text;
            装备.名字 = 装备名字.Text;
            装备.魔法 = 装备魔法.Text;
            装备.闪避 = 装备闪避.Text;
            装备.生命 = 装备生命.Text;
            装备.速度 = 装备速度.Text;
            装备.吸魔 = 装备吸魔.Text;
            装备.吸血 = 装备吸血.Text;
            装备.说明 = textBox4.Text;
            装备.ICO = 装备图标.Text;
            装备.命中 = 装备命中.Text;
            装备.主属性 = 主属性.Text;
            装备.suitID = 装备套装.Text;
           处理.保存文件(RC4.EncryptRC4wq(JsonConvert.SerializeObject(装备), 数据处理.获取密钥(1)), 数据处理.装备定义配置路径 + 数据处理.GetStringHash(RC4.EncryptRC4wq(装备.ID, 数据处理.获取密钥(2))) + ".dat");
            

        }

        private void button2_Click(object sender, EventArgs e)
        {
            装备类型 装备 = new 装备类型();
            装备.ID = 装备_ID.Text;
            装备.抵消 = 装备抵消.Text;
            装备.防御 = 装备防御.Text;
            装备.攻击 = 装备攻击.Text;
            装备.加深 = 装备加深.Text;
            装备.类型 = 装备类型.Text;
            装备.名字 = 装备名字.Text;
            装备.魔法 = 装备魔法.Text;
            装备.闪避 = 装备闪避.Text;
            装备.生命 = 装备生命.Text;
            装备.速度 = 装备速度.Text;
            装备.吸魔 = 装备吸魔.Text;
            装备.吸血 = 装备吸血.Text;
            装备.说明 = textBox4.Text;
            装备.ICO = 装备图标.Text;
            装备.命中 = 装备命中.Text;
            装备.主属性 = 主属性.Text;
            if (File.Exists(数据处理.装备定义配置路径 + 数据处理.GetStringHash(RC4.EncryptRC4wq(装备.ID, 数据处理.获取密钥(2))) + ".dat"))
            {
                MessageBox.Show("增加装备失败,序号已存在!");
            }else
            {
           new 数据处理().保存文件(RC4.EncryptRC4wq(JsonConvert.SerializeObject(装备), 数据处理.获取密钥(1)), 数据处理.装备定义配置路径 + 数据处理.GetStringHash(RC4.EncryptRC4wq(装备.ID, 数据处理.获取密钥(2))) + ".dat");

            }
           
        }
    }
}
