<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

  <system.webServer>
    <modules>
      <remove name="TelemetryCorrelationHttpModule" xdt:Transform="Remove" xdt:Locator="Match(name)"/>
      <add name="TelemetryCorrelationHttpModule" 
           type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation"
           preCondition="integratedMode,managedHandler" xdt:Transform="Remove" xdt:Locator="Match(type)"/>
    </modules>
  </system.webServer>
</configuration>