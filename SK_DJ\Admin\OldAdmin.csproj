﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9CB89AFD-A914-43CE-B3DA-629C1EACB4FC}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Admin</RootNamespace>
    <AssemblyName>Admin</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>PetShikong.pfx</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\WindowsFormsApplication7\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>7</LangVersion>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\WindowsFormsApplication7\bin\Debug\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="FROM.cs" />
    <Compile Include="PropInfo.cs" />
    <Compile Include="任务管理.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="任务管理.Designer.cs">
      <DependentUpon>任务管理.cs</DependentUpon>
    </Compile>
    <Compile Include="套装管理.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="套装管理.Designer.cs">
      <DependentUpon>套装管理.cs</DependentUpon>
    </Compile>
    <Compile Include="宠物进化路线.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="宠物进化路线.Designer.cs">
      <DependentUpon>宠物进化路线.cs</DependentUpon>
    </Compile>
    <Compile Include="装备列表.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="装备列表.Designer.cs">
      <DependentUpon>装备列表.cs</DependentUpon>
    </Compile>
    <Compile Include="怪物列表.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="怪物列表.Designer.cs">
      <DependentUpon>怪物列表.cs</DependentUpon>
    </Compile>
    <Compile Include="怪物加入列表.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="怪物加入列表.Designer.cs">
      <DependentUpon>怪物加入列表.cs</DependentUpon>
    </Compile>
    <Compile Include="管理窗口.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="管理窗口.Designer.cs">
      <DependentUpon>管理窗口.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="宠物列表.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="宠物列表.Designer.cs">
      <DependentUpon>宠物列表.cs</DependentUpon>
    </Compile>
    <Compile Include="输入框.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="输入框.Designer.cs">
      <DependentUpon>输入框.cs</DependentUpon>
    </Compile>
    <Compile Include="道具列表.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="道具列表.Designer.cs">
      <DependentUpon>道具列表.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="任务管理.resx">
      <DependentUpon>任务管理.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="套装管理.resx">
      <DependentUpon>套装管理.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="宠物进化路线.resx">
      <DependentUpon>宠物进化路线.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="装备列表.resx">
      <DependentUpon>装备列表.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="怪物列表.resx">
      <DependentUpon>怪物列表.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="怪物加入列表.resx">
      <DependentUpon>怪物加入列表.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="管理窗口.resx">
      <DependentUpon>管理窗口.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="宠物列表.resx">
      <DependentUpon>宠物列表.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="输入框.resx">
      <DependentUpon>输入框.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="道具列表.resx">
      <DependentUpon>道具列表.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="PetShikong.pfx" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\WindowsFormsApplication7\PetShikong.csproj">
      <Project>{179d3d5d-d3fd-4d07-84cf-cb17bb26a5ff}</Project>
      <Name>PetShikong</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.6.1">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.6.1 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>