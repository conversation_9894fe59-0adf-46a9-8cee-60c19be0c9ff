﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Helpers</name>
  </assembly>
  <members>
    <member name="T:System.Web.Helpers.Chart">
      <summary>以图表格式显示数据。</summary>
    </member>
    <member name="M:System.Web.Helpers.Chart.#ctor(System.Int32,System.Int32,System.String,System.String)">
      <summary>初始化 <see cref="T:System.Web.Helpers.Chart" /> 类的新实例。</summary>
      <param name="width">整个图表图像的宽度（以像素为单位）。</param>
      <param name="height">整个图表图像的高度（以像素为单位）。</param>
      <param name="theme">（可选）要应用到图表的模板（主题）。</param>
      <param name="themePath">（可选）要应用到图表的模板（主题）路径和文件名。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.AddLegend(System.String,System.String)">
      <summary>将图例添加到图表中。</summary>
      <returns>图表。</returns>
      <param name="title">图例标题的文本。</param>
      <param name="name">图例的唯一名称。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.AddSeries(System.String,System.String,System.String,System.String,System.String,System.Int32,System.Collections.IEnumerable,System.String,System.Collections.IEnumerable,System.String)">
      <summary>提供图表的数据点和系列特性。</summary>
      <returns>图表。</returns>
      <param name="name">系列的唯一名称。</param>
      <param name="chartType">系列的图表类型。</param>
      <param name="chartArea">用于绘制数据系列的图表区域的名称。</param>
      <param name="axisLabel">系列的轴标签文本。</param>
      <param name="legend">与图例关联的系列的名称。</param>
      <param name="markerStep">数据点标记的粒度。</param>
      <param name="xValue">要沿 X 轴绘制的值。</param>
      <param name="xField">用于 X 值的字段的名称。</param>
      <param name="yValues">要沿 Y 轴绘制的值。</param>
      <param name="yFields">一个逗号分隔列表，其中列出了 Y 值的字段的名称。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.AddTitle(System.String,System.String)">
      <summary>将标题添加到图表。</summary>
      <returns>图表。</returns>
      <param name="text">标题文本。</param>
      <param name="name">标题的唯一名称。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.DataBindCrossTable(System.Collections.IEnumerable,System.String,System.String,System.String,System.String,System.String)">
      <summary>将图表绑定到数据表，该表为列中的每个唯一值创建了一个系列。</summary>
      <returns>图表。</returns>
      <param name="dataSource">图表数据源。</param>
      <param name="groupByField">用于将数据分组成系列的列的名称。</param>
      <param name="xField">用于 X 值的列的名称。</param>
      <param name="yFields">一个逗号分隔列表，其中列出了用于 Y 值的列的名称。</param>
      <param name="otherFields">可以绑定的其他数据点属性。</param>
      <param name="pointSortOrder">对系列进行排序所依据的顺序。默认值为“升序”。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.DataBindTable(System.Collections.IEnumerable,System.String)">
      <summary>创建系列数据并将其绑定到指定数据表，然后选择性地填充多个 X 值。</summary>
      <returns>图表。</returns>
      <param name="dataSource">图表数据源。此数据源可以为任何 <see cref="T:System.Collections.IEnumerable" /> 对象。</param>
      <param name="xField">用于系列 X 值的表列的名称。</param>
    </member>
    <member name="P:System.Web.Helpers.Chart.FileName">
      <summary>获取或设置包含图表图像的文件的名称。</summary>
      <returns>文件名。</returns>
    </member>
    <member name="M:System.Web.Helpers.Chart.GetBytes(System.String)">
      <summary>以字节数组的形式返回图表图像。</summary>
      <returns>图表。</returns>
      <param name="format">图像格式。默认值为“jpeg”。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.GetFromCache(System.String)">
      <summary>从缓存中检索指定图表。</summary>
      <returns>图表。</returns>
      <param name="key">包含要检索的图表的缓存项的 ID。此键在调用 <see cref="M:System.Web.Helpers.Chart.SaveToCache(System.String,System.Int32,System.Boolean)" /> 方法时设置。</param>
    </member>
    <member name="P:System.Web.Helpers.Chart.Height">
      <summary>获取或设置图表图像的高度（以像素为单位）。</summary>
      <returns>图表高度。</returns>
    </member>
    <member name="M:System.Web.Helpers.Chart.Save(System.String,System.String)">
      <summary>将图表图像保存到指定文件中。</summary>
      <returns>图表。</returns>
      <param name="path">图像文件的位置和名称。</param>
      <param name="format">图像文件格式，如“png”或“jpeg”。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.SaveToCache(System.String,System.Int32,System.Boolean)">
      <summary>将图表保存到系统缓存中。</summary>
      <returns>包含图表的缓存项的 ID。</returns>
      <param name="key">缓存中图表的 ID。</param>
      <param name="minutesToCache">在缓存中保留图表图像的分钟数。默认值为 20。</param>
      <param name="slidingExpiration">若为 true，则指示每次访问项时都重置图表缓存项的过期；若为 false，则指示过期将基于自向缓存中添加项以来的绝对时间间隔。默认值为 true。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.SaveXml(System.String)">
      <summary>将图表另存为 XML 文件。</summary>
      <returns>图表。</returns>
      <param name="path">XML 文件的路径和名称。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.SetXAxis(System.String,System.Double,System.Double)">
      <summary>设置水平轴的值。</summary>
      <returns>图表。</returns>
      <param name="title">X 轴的标题。</param>
      <param name="min">X 轴的最小值。</param>
      <param name="max">X 轴的最大值。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.SetYAxis(System.String,System.Double,System.Double)">
      <summary>设置垂直轴的值。</summary>
      <returns>图表。</returns>
      <param name="title">Y 轴的标题。</param>
      <param name="min">Y 轴的最小值。</param>
      <param name="max">Y 轴的最大值。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.ToWebImage(System.String)">
      <summary>基于当前 <see cref="T:System.Web.Helpers.Chart" /> 对象创建 <see cref="T:System.Web.Helpers.WebImage" /> 对象。</summary>
      <returns>图表。</returns>
      <param name="format">将 <see cref="T:System.Web.Helpers.WebImage" /> 对象另存为某种图像时，该图像的格式。默认值为“jpeg”。<paramref name="format" /> 参数不区分大小写。</param>
    </member>
    <member name="P:System.Web.Helpers.Chart.Width">
      <summary>获取或设置图表图像的宽度（以像素为单位）。</summary>
      <returns>图表宽度。</returns>
    </member>
    <member name="M:System.Web.Helpers.Chart.Write(System.String)">
      <summary>将 <see cref="T:System.Web.Helpers.Chart" /> 对象的输出呈现为图像。</summary>
      <returns>图表。</returns>
      <param name="format">图像的格式。默认值为“jpeg”。</param>
    </member>
    <member name="M:System.Web.Helpers.Chart.WriteFromCache(System.String,System.String)">
      <summary>将已存入缓存的 <see cref="T:System.Web.Helpers.Chart" /> 对象的输入呈现为图像。</summary>
      <returns>图表。</returns>
      <param name="key">缓存中图表的 ID。</param>
      <param name="format">图像的格式。默认值为“jpeg”。</param>
    </member>
    <member name="T:System.Web.Helpers.ChartTheme">
      <summary>为 <see cref="T:System.Web.Helpers.Chart" /> 对象指定视觉主题。</summary>
    </member>
    <member name="F:System.Web.Helpers.ChartTheme.Blue">
      <summary>以一个具有渐进蓝色、圆角边缘、阴影和高对比度网格线的视觉容器为特色的 2D 图表的主题。</summary>
    </member>
    <member name="F:System.Web.Helpers.ChartTheme.Green">
      <summary>以一个具有渐进绿色、圆角边缘、阴影和低对比度网格线的视觉容器为特色的 2D 图表的主题。</summary>
    </member>
    <member name="F:System.Web.Helpers.ChartTheme.Vanilla">
      <summary>没有视觉容器和网格线的 2D 图表的主题。</summary>
    </member>
    <member name="F:System.Web.Helpers.ChartTheme.Vanilla3D">
      <summary>没有视觉容器、带有有限标签和稀疏高对比度网格线的 3D 图表的主题。</summary>
    </member>
    <member name="F:System.Web.Helpers.ChartTheme.Yellow">
      <summary>以一个具有渐进黄色、圆角边缘、阴影和高对比度网格线的视觉容器为特色的 2D 图表的主题。</summary>
    </member>
    <member name="T:System.Web.Helpers.Crypto">
      <summary>提供用于生成哈希值及加密密码或其他敏感数据的方法。</summary>
    </member>
    <member name="M:System.Web.Helpers.Crypto.GenerateSalt(System.Int32)">
      <summary>生成随机字节值的强密码序列。</summary>
      <returns>以 base-64 编码字符串生成的 salt 值。</returns>
      <param name="byteLength">要生成的加密随机字节的数量。</param>
    </member>
    <member name="M:System.Web.Helpers.Crypto.Hash(System.Byte[],System.String)">
      <summary>返回指定字节数组的哈希值。</summary>
      <returns>由十六进制字符组成的字符串形式的 <paramref name="input" /> 的哈希值。</returns>
      <param name="input">要为其提供哈希值的数据。</param>
      <param name="algorithm">用于生成哈希值的算法。默认值为“sha256”。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 为 null。</exception>
    </member>
    <member name="M:System.Web.Helpers.Crypto.Hash(System.String,System.String)">
      <summary>返回指定字符串的哈希值。</summary>
      <returns>由十六进制字符组成的字符串形式的 <paramref name="input" /> 的哈希值。</returns>
      <param name="input">要为其提供哈希值的数据。</param>
      <param name="algorithm">用于生成哈希值的算法。默认值为“sha256”。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 为 null。</exception>
    </member>
    <member name="M:System.Web.Helpers.Crypto.HashPassword(System.String)">
      <summary>返回指定密码的 RFC 2898 哈希值。</summary>
      <returns>base-64 编码字符串形式的 <paramref name="password" /> 的哈希值。</returns>
      <param name="password">要为其生成哈希值的密码。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="password" /> 为 null。</exception>
    </member>
    <member name="M:System.Web.Helpers.Crypto.SHA1(System.String)">
      <summary>返回指定字符串的 SHA-1 哈希值。</summary>
      <returns>由十六进制字符组成的字符串形式的 <paramref name="input" /> 的 SHA-1 哈希值。</returns>
      <param name="input">要为其提供哈希值的数据。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 为 null。</exception>
    </member>
    <member name="M:System.Web.Helpers.Crypto.SHA256(System.String)">
      <summary>返回指定字符串的 SHA-256 哈希值。</summary>
      <returns>由十六进制字符组成的字符串形式的 <paramref name="input" /> 的 SHA-256 哈希值。</returns>
      <param name="input">要为其提供哈希值的数据。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="input" /> 为 null。</exception>
    </member>
    <member name="M:System.Web.Helpers.Crypto.VerifyHashedPassword(System.String,System.String)">
      <summary>确定指定的 RFC 2898 哈希和密码是否为加密匹配。</summary>
      <returns>如果哈希值为密码的加密匹配，则为 true；否则为 false。</returns>
      <param name="hashedPassword">base-64 编码字符串形式的以前计算的 RFC 2898 哈希值。</param>
      <param name="password">要与 <paramref name="hashedPassword" /> 进行加密比较的明文密码。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="hashedPassword" /> 或 <paramref name="password" /> 为 null。</exception>
    </member>
    <member name="T:System.Web.Helpers.DynamicJsonArray">
      <summary>通过使用动态语言运行时 (DLR) 的动态功能将一系列值表示为类似 JavaScript 的数组。</summary>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonArray.#ctor(System.Object[])">
      <summary>使用指定的数组元素值初始化 <see cref="T:System.Web.Helpers.DynamicJsonArray" /> 类的新实例。</summary>
      <param name="arrayValues">包含要添加到 <see cref="T:System.Web.Helpers.DynamicJsonArray" /> 实例中的值的对象数组。</param>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonArray.GetEnumerator">
      <summary>返回一个可用于循环访问 <see cref="T:System.Web.Helpers.DynamicJsonArray" /> 实例的元素的枚举器。</summary>
      <returns>可用于循环访问 JSON 数组的元素的枚举器。</returns>
    </member>
    <member name="P:System.Web.Helpers.DynamicJsonArray.Item(System.Int32)">
      <summary>返回 <see cref="T:System.Web.Helpers.DynamicJsonArray" /> 实例中指定索引处的值。</summary>
      <returns>指定索引处的值。</returns>
    </member>
    <member name="P:System.Web.Helpers.DynamicJsonArray.Length">
      <summary>返回 <see cref="T:System.Web.Helpers.DynamicJsonArray" /> 实例中的元素数。</summary>
      <returns>JSON 数组中的元素数。</returns>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonArray.op_Implicit(System.Web.Helpers.DynamicJsonArray)~System.Object[]">
      <summary>将 <see cref="T:System.Web.Helpers.DynamicJsonArray" /> 实例转换为对象数组。</summary>
      <returns>表示 JSON 数组的对象数组。</returns>
      <param name="obj">要转换的 JSON 数组。</param>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonArray.op_Implicit(System.Web.Helpers.DynamicJsonArray)~System.Array">
      <summary>将 <see cref="T:System.Web.Helpers.DynamicJsonArray" /> 实例转换为对象数组。</summary>
      <returns>表示 JSON 数组的对象数组。</returns>
      <param name="obj">要转换的 JSON 数组。</param>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonArray.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回一个可用于循环访问集合的枚举器。</summary>
      <returns>一个可用于循环访问集合的枚举器。</returns>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonArray.TryConvert(System.Dynamic.ConvertBinder,System.Object@)">
      <summary>将 <see cref="T:System.Web.Helpers.DynamicJsonArray" /> 实例转换为兼容类型。</summary>
      <returns>如果转换成功，则为 true；否则为 false。</returns>
      <param name="binder">提供有关转换操作的信息。</param>
      <param name="result">此方法返回时，将包含类型转换操作的结果。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonArray.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
      <summary>以不会引发异常的方式测试动态成员（不受支持）的 <see cref="T:System.Web.Helpers.DynamicJsonArray" /> 实例。</summary>
      <returns>所有情况下均为 true。</returns>
      <param name="binder">提供有关 get 操作的信息。</param>
      <param name="result">此方法返回时，将包含 null。该参数未经初始化即被传递。</param>
    </member>
    <member name="T:System.Web.Helpers.DynamicJsonObject">
      <summary>通过使用动态语言运行时的功能将值的集合表示为类似 JavaScript 的对象。</summary>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonObject.#ctor(System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>使用指定字段值初始化 <see cref="T:System.Web.Helpers.DynamicJsonObject" /> 类的新实例。</summary>
      <param name="values">将作为动态成员添加到 <see cref="T:System.Web.Helpers.DynamicJsonObject" /> 实例中的属性名称和值的字典。</param>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonObject.GetDynamicMemberNames">
      <summary>返回包含 <see cref="T:System.Web.Helpers.DynamicJsonObject" /> 实例的所有动态成员（JSON 字段）的名称的列表。</summary>
      <returns>包含每个动态成员（JSON 字段）的名称的列表。</returns>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonObject.TryConvert(System.Dynamic.ConvertBinder,System.Object@)">
      <summary>将 <see cref="T:System.Web.Helpers.DynamicJsonObject" /> 实例转换为兼容类型。</summary>
      <returns>所有情况下均为 true。</returns>
      <param name="binder">提供有关转换操作的信息。</param>
      <param name="result">此方法返回时，将包含类型转换操作的结果。该参数未经初始化即被传递。</param>
      <exception cref="T:System.InvalidOperationException">无法将 <see cref="T:System.Web.Helpers.DynamicJsonObject" /> 实例转换为指定类型。</exception>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonObject.TryGetIndex(System.Dynamic.GetIndexBinder,System.Object[],System.Object@)">
      <summary>使用指定索引获取 <see cref="T:System.Web.Helpers.DynamicJsonObject" /> 字段的值。</summary>
      <returns>所有情况下均为 true。</returns>
      <param name="binder">提供有关已编入索引的 get 操作的信息。</param>
      <param name="indexes">包含按名称将字段编入索引的单个对象的数组。此对象必须能够转换为字符串，以便指定要返回的 JSON 字段的名称。如果指定了多个索引，则当此方法返回时，<paramref name="result" /> 将包含 null。</param>
      <param name="result">当此方法返回时，将包含已编入索引的字段的值；或者如果 get 操作失败，则将包含 null。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonObject.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
      <summary>使用指定名称获取 <see cref="T:System.Web.Helpers.DynamicJsonObject" /> 字段的值。</summary>
      <returns>所有情况下均为 true。</returns>
      <param name="binder">提供有关 get 操作的信息。</param>
      <param name="result">当此方法返回时，将包含字段的值；或者如果 GET 操作失败，则将包含 null。该参数未经初始化即被传递。</param>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonObject.TrySetIndex(System.Dynamic.SetIndexBinder,System.Object[],System.Object)">
      <summary>使用指定索引设置 <see cref="T:System.Web.Helpers.DynamicJsonObject" /> 字段的值。</summary>
      <returns>所有情况下均为 true。</returns>
      <param name="binder">提供有关已编入索引的 SET 操作的信息。</param>
      <param name="indexes">包含按名称将字段编入索引的单个对象的数组。此对象必须能够转换为字符串，以便指定要返回的 JSON 字段的名称。如果指定了多个索引，则不会更改或添加任何字段。</param>
      <param name="value">要将字段设置为的值。</param>
    </member>
    <member name="M:System.Web.Helpers.DynamicJsonObject.TrySetMember(System.Dynamic.SetMemberBinder,System.Object)">
      <summary>使用指定名称设置 <see cref="T:System.Web.Helpers.DynamicJsonObject" /> 字段的值。</summary>
      <returns>所有情况下均为 true。</returns>
      <param name="binder">提供有关 SET 操作的信息。</param>
      <param name="value">要将字段设置为的值。</param>
    </member>
    <member name="T:System.Web.Helpers.Json">
      <summary>提供处理 JavaScript 对象表示法 (JSON) 格式数据的方法。</summary>
    </member>
    <member name="M:System.Web.Helpers.Json.Decode``1(System.String)">
      <summary>将 JavaScript 对象表示法 (JSON) 格式的数据转换为指定的强类型数据列表。</summary>
      <returns>已转换为强类型列表的 JSON 编码数据。</returns>
      <param name="value">要转换的 JSON 编码字符串。</param>
      <typeparam name="T">要将 JSON 数据转换为的强类型列表的类型。</typeparam>
    </member>
    <member name="M:System.Web.Helpers.Json.Decode(System.String)">
      <summary>将 JavaScript 对象表示法 (JSON) 格式的数据转换为数据对象。</summary>
      <returns>已转换为数据对象的 JSON 编码数据。</returns>
      <param name="value">要转换的 JSON 编码字符串。</param>
    </member>
    <member name="M:System.Web.Helpers.Json.Decode(System.String,System.Type)">
      <summary>将 JavaScript 对象表示法 (JSON) 格式的数据转换为指定类型的数据对象。</summary>
      <returns>已转换为指定类型的 JSON 编码数据。</returns>
      <param name="value">要转换的 JSON 编码字符串。</param>
      <param name="targetType">应将 <paramref name="value" /> 数据转换为的类型。</param>
    </member>
    <member name="M:System.Web.Helpers.Json.Encode(System.Object)">
      <summary>将数据对象转换为 JavaScript 对象表示法 (JSON) 格式的字符串。</summary>
      <returns>返回已转换为 JSON 格式的数据的字符串。</returns>
      <param name="value">要转换的数据对象。</param>
    </member>
    <member name="M:System.Web.Helpers.Json.Write(System.Object,System.IO.TextWriter)">
      <summary>将数据对象转换为 JavaScript 对象表示法 (JSON) 格式的字符串，然后将该字符串添加到指定的 <see cref="T:System.IO.TextWriter" /> 对象。</summary>
      <param name="value">要转换的数据对象。</param>
      <param name="writer">包含已转换的 JSON 数据的对象。</param>
    </member>
    <member name="T:System.Web.Helpers.ObjectInfo">
      <summary>呈现指定对象及其引用的任何子对象的属性名称和值。</summary>
    </member>
    <member name="M:System.Web.Helpers.ObjectInfo.Print(System.Object,System.Int32,System.Int32)">
      <summary>呈现指定对象及任何子对象的属性名称和值。</summary>
      <returns>对于简单变量，将返回类型和值。对于包含多个项的对象，将返回属性名称或键，以及每个属性的值。</returns>
      <param name="value">要呈现其信息的对象。</param>
      <param name="depth">可选。指定要呈现其信息的嵌套子对象的深度。默认值为 10。</param>
      <param name="enumerationLength">可选。指定该方法为对象值显示的最大字符数。默认值为 1000。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="depth" /> 小于零。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="enumerationLength" /> 小于或等于零。</exception>
    </member>
    <member name="T:System.Web.Helpers.ServerInfo">
      <summary>显示有关承载当前网页的 Web 服务器环境的信息。</summary>
    </member>
    <member name="M:System.Web.Helpers.ServerInfo.GetHtml">
      <summary>显示有关 Web 服务器环境的信息。</summary>
      <returns>包含 Web 服务器相关信息的名称/值对字符串。</returns>
    </member>
    <member name="T:System.Web.Helpers.SortDirection">
      <summary>指定对项列表进行排序的方向。</summary>
    </member>
    <member name="F:System.Web.Helpers.SortDirection.Ascending">
      <summary>从最小到最大排序 — 例如，从 1 到 10。</summary>
    </member>
    <member name="F:System.Web.Helpers.SortDirection.Descending">
      <summary>从最大到最小排序 — 例如，从 10 到 1。</summary>
    </member>
    <member name="T:System.Web.Helpers.WebCache">
      <summary>提供可存储经常访问的数据的缓存。</summary>
    </member>
    <member name="M:System.Web.Helpers.WebCache.Get(System.String)">
      <summary>从 <see cref="T:System.Web.Helpers.WebCache" /> 对象中检索指定项。</summary>
      <returns>从缓存中检索到的项；如果找不到该项，则为 null。</returns>
      <param name="key">要检索的缓存项的标识符。</param>
    </member>
    <member name="M:System.Web.Helpers.WebCache.Remove(System.String)">
      <summary>从 <see cref="T:System.Web.Helpers.WebCache" /> 对象中删除指定项。</summary>
      <returns>从 <see cref="T:System.Web.Helpers.WebCache" /> 对象中删除的项。如果找不到该项，则返回 null。</returns>
      <param name="key">要删除的缓存项的标识符。</param>
    </member>
    <member name="M:System.Web.Helpers.WebCache.Set(System.String,System.Object,System.Int32,System.Boolean)">
      <summary>将一个项插入到 <see cref="T:System.Web.Helpers.WebCache" /> 对象。</summary>
      <param name="key">缓存项的标识符。</param>
      <param name="value">要插入缓存中的数据。</param>
      <param name="minutesToCache">可选。在缓存中保留项的分钟数。默认值为 20。</param>
      <param name="slidingExpiration">可选。若为 true，则指示每次访问项时都重置缓存项过期；若为 false，则指示过期将基于自向缓存中添加项以来的绝对时间。默认值为 true。在这种情况下，如果还使用 <paramref name="minutesToCache" /> 参数的默认值，缓存的项将在最后一次访问后 20 分钟过期。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="minutesToCache" /> 的值小于或等于零。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">已启用可调过期且 <paramref name="minutesToCache" /> 的值大于一年。</exception>
    </member>
    <member name="T:System.Web.Helpers.WebGrid">
      <summary>在网页上使用 HTML table 元素显示数据。</summary>
    </member>
    <member name="M:System.Web.Helpers.WebGrid.#ctor(System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.String},System.String,System.Int32,System.Boolean,System.Boolean,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>初始化 <see cref="T:System.Web.Helpers.WebGrid" /> 类的新实例。</summary>
      <param name="source">要显示的数据。</param>
      <param name="columnNames">包含要显示的数据列的名称的集合。默认情况下，将根据 <paramref name="source" /> 参数中的值自动填充此值。</param>
      <param name="defaultSort">默认情况下用于对网格进行排序的数据列的名称。</param>
      <param name="rowsPerPage">启用分页时在网格的每个页上显示的行的数量。默认值为 10。</param>
      <param name="canPage">若要指定为 <see cref="T:System.Web.Helpers.WebGrid" /> 实例启用分页，则为 true；否则为 false。默认值为 true。</param>
      <param name="canSort">若要指定为 <see cref="T:System.Web.Helpers.WebGrid" /> 实例启用排序，则为 true；否则为 false。默认值为 true。</param>
      <param name="ajaxUpdateContainerId">HTML id 特性的值，用于标记 HTML 元素以获取与 <see cref="T:System.Web.Helpers.WebGrid" /> 实例关联的动态 Ajax 更新。</param>
      <param name="ajaxUpdateCallback">在更新 <see cref="P:System.Web.Helpers.WebGrid.AjaxUpdateContainerId" /> 属性指定的 HTML 元素后调用的 JavaScript 函数的名称。如果未提供函数名称，将不会调用任何函数。如果指定函数不存在，在调用该函数时，将发生 JavaScript 错误。</param>
      <param name="fieldNamePrefix">可应用于所有与 <see cref="T:System.Web.Helpers.WebGrid" /> 实例关联的查询字符串字段的前缀。此值用于支持同一网页上的多个 <see cref="T:System.Web.Helpers.WebGrid" /> 实例。</param>
      <param name="pageFieldName">用于指定 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的当前页的查询字符串字段的名称。</param>
      <param name="selectionFieldName">用于指定 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的当前选定行的查询字符串字段的名称。</param>
      <param name="sortFieldName">查询字符串字段（用于指定作为 <see cref="T:System.Web.Helpers.WebGrid" /> 实例排序依据的数据列的名称）的名称。</param>
      <param name="sortDirectionFieldName">用于指定 <see cref="T:System.Web.Helpers.WebGrid" /> 实例排序方向的查询字符串字段的名称。</param>
    </member>
    <member name="M:System.Web.Helpers.WebGrid.AddSorter``2(System.String,System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>为给定列添加特定的排序函数。</summary>
      <returns>应用了新的自定义排序程序的当前网格。</returns>
      <param name="columnName">列名称（用于排序）</param>
      <param name="keySelector">用于为网格源中的每个元素选择键或排序依据的函数。</param>
      <typeparam name="TElement">网格源中的元素类型。</typeparam>
      <typeparam name="TProperty">列类型，通常从 keySelector 函数的返回类型推断。</typeparam>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.AjaxUpdateCallback">
      <summary>在更新与 <see cref="T:System.Web.Helpers.WebGrid" /> 实例关联的 HTML 元素以响应 Ajax 更新请求后，获取要调用的 JavaScript 函数的名称。</summary>
      <returns>函数的名称。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.AjaxUpdateContainerId">
      <summary>获取在网页上标记 HTML 元素（该元素获取与 <see cref="T:System.Web.Helpers.WebGrid" /> 实例关联的动态 Ajax 更新）的 HTML id 特性的值。</summary>
      <returns>id 特性的值。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebGrid.Bind(System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.String},System.Boolean,System.Int32)">
      <summary>将指定数据绑定到 <see cref="T:System.Web.Helpers.WebGrid" /> 实例。</summary>
      <returns>已绑定并填充的 <see cref="T:System.Web.Helpers.WebGrid" /> 实例。</returns>
      <param name="source">要显示的数据。</param>
      <param name="columnNames">包含要绑定的数据列的名称的集合。</param>
      <param name="autoSortAndPage">若要为 <see cref="T:System.Web.Helpers.WebGrid" /> 实例启用排序和分页，则为 true；否则为 false。</param>
      <param name="rowCount">要在网格的每个页上显示的行的数量。</param>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.CanSort">
      <summary>获取指示 <see cref="T:System.Web.Helpers.WebGrid" /> 实例是否支持排序的值。</summary>
      <returns>如果该实例支持排序，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebGrid.Column(System.String,System.String,System.Func{System.Object,System.Object},System.String,System.Boolean)">
      <summary>创建新的 <see cref="T:System.Web.Helpers.WebGridColumn" /> 实例。</summary>
      <returns>新列。</returns>
      <param name="columnName">要与 <see cref="T:System.Web.Helpers.WebGridColumn" /> 实例关联的数据列的名称。</param>
      <param name="header">在 HTML 表列的标题中呈现的、与 <see cref="T:System.Web.Helpers.WebGridColumn" /> 实例关联的文本。</param>
      <param name="format">用于格式化与 <see cref="T:System.Web.Helpers.WebGridColumn" /> 实例关联的数据值的函数。</param>
      <param name="style">一个用于指定 CSS 类名称的字符串，而 CSS 类则可用于设置与 <see cref="T:System.Web.Helpers.WebGridColumn" /> 实例关联的 HTML 表单元格的样式。</param>
      <param name="canSort">若要在 <see cref="T:System.Web.Helpers.WebGrid" /> 实例中按 <see cref="T:System.Web.Helpers.WebGridColumn" /> 实例的关联数据值启用排序，则为 true；否则为 false。默认值为 true。</param>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.ColumnNames">
      <summary>获取一个集合，该集合包含绑定到 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的每个数据列的名称。</summary>
      <returns>数据列名称的集合。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebGrid.Columns(System.Web.Helpers.WebGridColumn[])">
      <summary>返回包含指定 <see cref="T:System.Web.Helpers.WebGridColumn" /> 实例的数组。</summary>
      <returns>列的数组。</returns>
      <param name="columnSet">
        <see cref="T:System.Web.Helpers.WebGridColumn" /> 列实例的数量可变。</param>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.FieldNamePrefix">
      <summary>获取可应用于所有与 <see cref="T:System.Web.Helpers.WebGrid" /> 实例关联的查询字符串字段的前缀。</summary>
      <returns>
        <see cref="T:System.Web.Helpers.WebGrid" /> 实例的查询字符串字段前缀。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebGrid.GetContainerUpdateScript(System.String)">
      <summary>返回可用于在指定网页上更新与 <see cref="T:System.Web.Helpers.WebGrid" /> 实例关联的 HTML 元素的 JavaScript 语句。</summary>
      <returns>可用于在网页上更新与 <see cref="T:System.Web.Helpers.WebGrid" /> 实例关联的 HTML 元素的 JavaScript 语句。</returns>
      <param name="path">包含所更新的 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的网页 URL。此 URL 可以包括查询字符串参数。</param>
    </member>
    <member name="M:System.Web.Helpers.WebGrid.GetHtml(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Boolean,System.Boolean,System.String,System.Collections.Generic.IEnumerable{System.Web.Helpers.WebGridColumn},System.Collections.Generic.IEnumerable{System.String},System.Web.Helpers.WebGridPagerModes,System.String,System.String,System.String,System.String,System.Int32,System.Object)">
      <summary>返回用于呈现 <see cref="T:System.Web.Helpers.WebGrid" /> 实例并使用指定分页选项的 HTML 标记。</summary>
      <returns>表示完全填充的 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的 HTML 标记。</returns>
      <param name="tableStyle">用于设置整个表的样式的 CSS 类的名称。</param>
      <param name="headerStyle">用于设置表标题样式的 CSS 类的名称。</param>
      <param name="footerStyle">用于设置表脚注样式的 CSS 类的名称。</param>
      <param name="rowStyle">用于设置每个表行样式的 CSS 类的名称。</param>
      <param name="alternatingRowStyle">用于设置偶数表行样式的 CSS 类的名称。</param>
      <param name="selectedRowStyle">用于设置选定表行样式的 CSS 类的名称。（一次只能选定一行。）</param>
      <param name="caption">表标题。</param>
      <param name="displayHeader">若要显示表标题，则为 true；否则为 false。默认值为 true。</param>
      <param name="fillEmptyRows">在没有足够数据项填充最后一页时，若要在最后一页中插入附加行，则为 true；否则为 false。默认值为 false。附加行使用由 <paramref name="emptyRowCellValue" /> 参数指定的文本进行填充。</param>
      <param name="emptyRowCellValue">在没有足够数据项填充最后一页时用于在页面中填充附加行的文本。必须将 <paramref name="fillEmptyRows" /> 参数设置为 true 才能显示这些附加行。</param>
      <param name="columns">指定如何显示每列的 <see cref="T:System.Web.Helpers.WebGridColumn" /> 实例的集合。其中包括哪个数据列与每个网格列相关联，以及如何格式化每个网格列包含的数据值。</param>
      <param name="exclusions">一个集合，其中包含在网格自动填充列时要排除的数据列的名称。</param>
      <param name="mode">一种枚举值的按位组合，可用于指定相关方法，以便在 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的页面间进行切换。</param>
      <param name="firstText">用于链接到 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的第一个页面的 HTML 链接元素的文本。必须设置 <paramref name="mode" /> 参数的 <see cref="F:System.Web.Helpers.WebGridPagerModes.FirstLast" /> 标记才能显示此页面导航元素。</param>
      <param name="previousText">用于链接到 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的上一个页面的 HTML 链接元素的文本。必须设置 <paramref name="mode" /> 参数的 <see cref="F:System.Web.Helpers.WebGridPagerModes.NextPrevious" /> 标记才能显示此页面导航元素。</param>
      <param name="nextText">用于链接到 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的下一个页面的 HTML 链接元素的文本。必须设置 <paramref name="mode" /> 参数的 <see cref="F:System.Web.Helpers.WebGridPagerModes.NextPrevious" /> 标记才能显示此页面导航元素。</param>
      <param name="lastText">用于链接到 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的最后一个页面的 HTML 链接元素的文本。必须设置 <paramref name="mode" /> 参数的 <see cref="F:System.Web.Helpers.WebGridPagerModes.FirstLast" /> 标记才能显示此页面导航元素。</param>
      <param name="numericLinksCount">提供给附近的 <see cref="T:System.Web.Helpers.WebGrid" /> 页的数字页链接的数量。每个数字页链接的文本都包含页码。必须设置 <paramref name="mode" /> 参数的 <see cref="F:System.Web.Helpers.WebGridPagerModes.Numeric" /> 标记才能显示这些页面导航元素。</param>
      <param name="htmlAttributes">一个表示特性（名称和值）集合的对象，可针对表示 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的 HTML table 元素进行设置。</param>
    </member>
    <member name="M:System.Web.Helpers.WebGrid.GetPageUrl(System.Int32)">
      <summary>返回可用于显示 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的指定数据页的 URL。</summary>
      <returns>可用于显示网格的指定数据页的 URL。</returns>
      <param name="pageIndex">要显示的 <see cref="T:System.Web.Helpers.WebGrid" /> 页的索引。</param>
    </member>
    <member name="M:System.Web.Helpers.WebGrid.GetSortUrl(System.String)">
      <summary>返回可用于按指定列对 <see cref="T:System.Web.Helpers.WebGrid" /> 实例进行排序的 URL。</summary>
      <returns>可用于对网格进行排序的 URL。</returns>
      <param name="column">要作为排序依据的数据列的名称。</param>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.HasSelection">
      <summary>获取指示是否已选定 <see cref="T:System.Web.Helpers.WebGrid" /> 实例中的一行的值。</summary>
      <returns>如果当前已选定一行，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.IsAjaxEnabled">
      <summary>返回一个值，用于指示 <see cref="T:System.Web.Helpers.WebGrid" /> 实例是否能够使用 Ajax 调用来刷新显示内容。</summary>
      <returns>如果该实例支持 Ajax 调用，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.PageCount">
      <summary>获取 <see cref="T:System.Web.Helpers.WebGrid" /> 实例包含的页数。</summary>
      <returns>页计数。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.PageFieldName">
      <summary>获取用于指定 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的当前页的查询字符串字段的全名。</summary>
      <returns>用于指定网格的当前页的查询字符串字段的全名。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.PageIndex">
      <summary>获取或设置 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的当前页的索引。</summary>
      <returns>当前页的索引。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebGrid.Pager(System.Web.Helpers.WebGridPagerModes,System.String,System.String,System.String,System.String,System.Int32)">
      <summary>返回用于为 <see cref="T:System.Web.Helpers.WebGrid" /> 实例提供指定分页支持的 HTML 标记。</summary>
      <returns>为网格提供分页支持的 HTML 标记。</returns>
      <param name="mode">一种枚举值的按位组合，可用于指定相关方法，以便在网格的页面间进行切换。默认值为 <see cref="F:System.Web.Helpers.WebGridPagerModes.NextPrevious" /> 和 <see cref="F:System.Web.Helpers.WebGridPagerModes.Numeric" /> 标记的按位或。</param>
      <param name="firstText">可导航到网格第一个页面的 HTML 链接元素的文本。</param>
      <param name="previousText">可导航到网格上一页面的 HTML 链接元素的文本。</param>
      <param name="nextText">可导航到网格下一页面的 HTML 链接元素的文本。</param>
      <param name="lastText">可导航到网格最后一个页面的 HTML 链接元素的文本。</param>
      <param name="numericLinksCount">要显示的数字页链接的数量。默认值为 5。</param>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.Rows">
      <summary>对网格排序后，获取包含 <see cref="T:System.Web.Helpers.WebGrid" /> 实例当前页上相关行的列表。</summary>
      <returns>行列表。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.RowsPerPage">
      <summary>获取在 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的每个页上显示的行的数量。</summary>
      <returns>在网格的每个页上显示的行的数量。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.SelectedIndex">
      <summary>获取或设置相对于 <see cref="T:System.Web.Helpers.WebGrid" /> 实例当前页的选定行的索引。</summary>
      <returns>相对于当前页的选定行的索引。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.SelectedRow">
      <summary>获取 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的当前选定行。</summary>
      <returns>当前选定行。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.SelectionFieldName">
      <summary>获取用于指定 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的选定行的查询字符串字段的全名。</summary>
      <returns>用于指定网格的选定行的查询字符串字段的全名。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.SortColumn">
      <summary>获取或设置作为 <see cref="T:System.Web.Helpers.WebGrid" /> 实例排序依据的数据列的名称。</summary>
      <returns>用于对网格进行排序的数据列的名称。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.SortDirection">
      <summary>获取或设置 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的排序方向。</summary>
      <returns>排序方向。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.SortDirectionFieldName">
      <summary>获取用于指定 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的排序方向的查询字符串字段的全名。</summary>
      <returns>用于指定网格的排序方向的查询字符串字段的全名。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.SortFieldName">
      <summary>获取查询字符串字段（用于指定作为 <see cref="T:System.Web.Helpers.WebGrid" /> 实例排序依据的数据列的名称）的全名。</summary>
      <returns>用于指定作为网格排序依据的数据列名称的查询字符串字段的全名。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebGrid.Table(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Boolean,System.Boolean,System.String,System.Collections.Generic.IEnumerable{System.Web.Helpers.WebGridColumn},System.Collections.Generic.IEnumerable{System.String},System.Func{System.Object,System.Object},System.Object)">
      <summary>返回用于呈现 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的 HTML 标记。</summary>
      <returns>表示完全填充的 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的 HTML 标记。</returns>
      <param name="tableStyle">用于设置整个表的样式的 CSS 类的名称。</param>
      <param name="headerStyle">用于设置表标题样式的 CSS 类的名称。</param>
      <param name="footerStyle">用于设置表脚注样式的 CSS 类的名称。</param>
      <param name="rowStyle">用于设置每个表行样式的 CSS 类的名称。</param>
      <param name="alternatingRowStyle">用于设置偶数表行样式的 CSS 类的名称。</param>
      <param name="selectedRowStyle">用于设置选定表行样式的 CSS 类的名称。</param>
      <param name="caption">表标题。</param>
      <param name="displayHeader">若要显示表标题，则为 true；否则为 false。默认值为 true。</param>
      <param name="fillEmptyRows">在没有足够数据项填充最后一页时，若要在最后一页中插入附加行，则为 true；否则为 false。默认值为 false。附加行使用由 <paramref name="emptyRowCellValue" /> 参数指定的文本进行填充。</param>
      <param name="emptyRowCellValue">在没有足够数据项填充最后一页时用于在最后一页中填充附加行的文本。必须将 <paramref name="fillEmptyRows" /> 参数设置为 true 才能显示这些附加行。</param>
      <param name="columns">指定如何显示每列的 <see cref="T:System.Web.Helpers.WebGridColumn" /> 实例的集合。其中包括哪个数据列与每个网格列相关联，以及如何格式化每个网格列包含的数据值。</param>
      <param name="exclusions">一个集合，其中包含在网格自动填充列时要排除的数据列的名称。</param>
      <param name="footer">可返回用于呈现表脚注的 HTML 标记的函数。</param>
      <param name="htmlAttributes">一个表示特性（名称和值）集合的对象，可针对表示 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的 HTML table 元素进行设置。</param>
    </member>
    <member name="P:System.Web.Helpers.WebGrid.TotalRowCount">
      <summary>获取 <see cref="T:System.Web.Helpers.WebGrid" /> 实例包含的行的总数。</summary>
      <returns>网格中的行的总数。此值包括每个页中的所有行，但不包括在没有足够数据项填充最后一页时插入到最后一页中的附加行。</returns>
    </member>
    <member name="T:System.Web.Helpers.WebGridColumn">
      <summary>表示 <see cref="T:System.Web.Helpers.WebGrid" /> 实例中的一列。</summary>
    </member>
    <member name="M:System.Web.Helpers.WebGridColumn.#ctor">
      <summary>初始化 <see cref="T:System.Web.Helpers.WebGridColumn" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Web.Helpers.WebGridColumn.CanSort">
      <summary>获取或设置指示是否可以对 <see cref="T:System.Web.Helpers.WebGrid" /> 列进行排序的值。</summary>
      <returns>若指示可以对该列进行排序，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGridColumn.ColumnName">
      <summary>获取或设置与 <see cref="T:System.Web.Helpers.WebGrid" /> 列关联的数据项的名称。</summary>
      <returns>数据项的名称。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGridColumn.Format">
      <summary>获取或设置一个函数，该函数用于设置与 <see cref="T:System.Web.Helpers.WebGrid" /> 列关联的数据项的格式。</summary>
      <returns>用于设置与该列关联的数据项格式的函数。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGridColumn.Header">
      <summary>获取或设置在 <see cref="T:System.Web.Helpers.WebGrid" /> 列的标题中呈现的文本。</summary>
      <returns>呈现到列标题的文本。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGridColumn.Style">
      <summary>获取或设置 CSS 类特性，该特性可以呈现为与 <see cref="T:System.Web.Helpers.WebGrid" /> 列关联的 HTML 表单元格的一部分。</summary>
      <returns>应用于与该列关联的单元格的 CSS 类特性。</returns>
    </member>
    <member name="T:System.Web.Helpers.WebGridPagerModes">
      <summary>指定标记，这些标记所描述的方法可用于在 <see cref="T:System.Web.Helpers.WebGrid" /> 实例的页面间进行切换。此枚举有一个 <see cref="T:System.FlagsAttribute" /> 特性，通过该特性可使其成员值按位组合。</summary>
    </member>
    <member name="F:System.Web.Helpers.WebGridPagerModes.All">
      <summary>指示已提供在 <see cref="T:System.Web.Helpers.WebGrid" /> 页面间进行切换的所有方法。</summary>
    </member>
    <member name="F:System.Web.Helpers.WebGridPagerModes.FirstLast">
      <summary>指示已提供可直接转到第一个或最后一个 <see cref="F:System.Web.Helpers.WebGrid" /> 页面的方法。</summary>
    </member>
    <member name="F:System.Web.Helpers.WebGridPagerModes.NextPrevious">
      <summary>指示已提供可转到下一个或上一个 <see cref="F:System.Web.Helpers.WebGrid" /> 页面的方法。</summary>
    </member>
    <member name="F:System.Web.Helpers.WebGridPagerModes.Numeric">
      <summary>指示已提供可通过使用页码转到附近的 <see cref="F:System.Web.Helpers.WebGrid" /> 页面的方法。</summary>
    </member>
    <member name="T:System.Web.Helpers.WebGridRow">
      <summary>表示 <see cref="T:System.Web.Helpers.WebGrid" /> 实例中的一行。</summary>
    </member>
    <member name="M:System.Web.Helpers.WebGridRow.#ctor(System.Web.Helpers.WebGrid,System.Object,System.Int32)">
      <summary>使用指定的 <see cref="T:System.Web.Helpers.WebGrid" /> 实例、行值和索引初始化 <see cref="T:System.Web.Helpers.WebGridRow" /> 类的新实例。</summary>
      <param name="webGrid">包含该行的 <see cref="T:System.Web.Helpers.WebGrid" /> 实例。</param>
      <param name="value">包含该行中每个值的属性成员的对象。</param>
      <param name="rowIndex">该行的索引。</param>
    </member>
    <member name="M:System.Web.Helpers.WebGridRow.GetEnumerator">
      <summary>返回一个可用于循环访问 <see cref="T:System.Web.Helpers.WebGridRow" /> 实例的值的枚举器。</summary>
      <returns>可用于循环访问行的值的枚举器。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebGridRow.GetSelectLink(System.String)">
      <summary>返回可供用户用来选择行的 HTML 元素（链接）。</summary>
      <returns>允许用户通过单击方式来选择行的链接。</returns>
      <param name="text">链接元素的内部文本。如果 <paramref name="text" /> 为空或 null，则使用“Select”。</param>
    </member>
    <member name="M:System.Web.Helpers.WebGridRow.GetSelectUrl">
      <summary>返回可用于选择行的 URL。</summary>
      <returns>用于选择行的 URL。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGridRow.Item(System.Int32)">
      <summary>返回 <see cref="T:System.Web.Helpers.WebGridRow" /> 实例中指定索引处的值。</summary>
      <returns>指定索引处的值。</returns>
      <param name="index">该行中要返回的值的从零开始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于 0，或大于等于行中值的数量。</exception>
    </member>
    <member name="P:System.Web.Helpers.WebGridRow.Item(System.String)">
      <summary>返回在 <see cref="T:System.Web.Helpers.WebGridRow" /> 实例中具有指定名称的值。</summary>
      <returns>指定值。</returns>
      <param name="name">该行中要返回的值的名称。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> 为 Nothing 或空。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="name" /> 指定不存在的值。</exception>
    </member>
    <member name="M:System.Web.Helpers.WebGridRow.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个可用于循环访问集合的枚举器。</summary>
      <returns>一个可用于循环访问集合的枚举器。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebGridRow.ToString">
      <summary>返回一个表示 <see cref="T:System.Web.Helpers.WebGridRow" /> 实例的所有值的字符串。</summary>
      <returns>表示行的值的字符串。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebGridRow.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
      <summary>返回指定联编程序所描述的 <see cref="T:System.Web.Helpers.WebGridRow" /> 成员的值。</summary>
      <returns>如果成功检索了项的值，则为 true；否则为 false。</returns>
      <param name="binder">已绑定的属性成员的 getter。</param>
      <param name="result">此方法返回时，其中包含的对象保留了 <paramref name="binder" /> 所描述的项的值。该参数未经初始化即被传递。</param>
    </member>
    <member name="P:System.Web.Helpers.WebGridRow.Value">
      <summary>获取包含该行中每个值的属性成员的对象。</summary>
      <returns>以属性的形式包含该行中每个值的对象。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebGridRow.WebGrid">
      <summary>获取该行所属的 <see cref="T:System.Web.Helpers.WebGrid" /> 实例。</summary>
      <returns>包含该行的 <see cref="T:System.Web.Helpers.WebGrid" /> 实例。</returns>
    </member>
    <member name="T:System.Web.Helpers.WebImage">
      <summary>表示用于显示和管理网页中图像的对象。</summary>
    </member>
    <member name="M:System.Web.Helpers.WebImage.#ctor(System.Byte[])">
      <summary>使用可表示图像的字节数组来初始化 <see cref="T:System.Web.Helpers.WebImage" /> 类的新实例。</summary>
      <param name="content">图像。</param>
    </member>
    <member name="M:System.Web.Helpers.WebImage.#ctor(System.IO.Stream)">
      <summary>使用可表示图像的流来初始化 <see cref="T:System.Web.Helpers.WebImage" /> 类的新实例。</summary>
      <param name="imageStream">图像。</param>
    </member>
    <member name="M:System.Web.Helpers.WebImage.#ctor(System.String)">
      <summary>使用可表示图像位置的路径来初始化 <see cref="T:System.Web.Helpers.WebImage" /> 类的新实例。</summary>
      <param name="filePath">包含图像的文件的路径。</param>
    </member>
    <member name="M:System.Web.Helpers.WebImage.AddImageWatermark(System.String,System.Int32,System.Int32,System.String,System.String,System.Int32,System.Int32)">
      <summary>使用水印图像的路径添加水印图像。</summary>
      <returns>打了水印的图像。</returns>
      <param name="watermarkImageFilePath">包含水印图像的文件的路径。</param>
      <param name="width">水印图像的宽度（以像素为单位）。</param>
      <param name="height">水印图像的高度（以像素为单位）。</param>
      <param name="horizontalAlign">水印图像的水平对齐。值可以为“靠左”、“靠右”或“居中”。</param>
      <param name="verticalAlign">水印图像的垂直对齐。值可以为“靠上”、“居中”或“靠下”。</param>
      <param name="opacity">水印图像的不透明度，已指定为 0 和 100 之间的某个值。</param>
      <param name="padding">水印图像周围的边距的大小（以像素为单位）。</param>
    </member>
    <member name="M:System.Web.Helpers.WebImage.AddImageWatermark(System.Web.Helpers.WebImage,System.Int32,System.Int32,System.String,System.String,System.Int32,System.Int32)">
      <summary>使用指定图像对象添加水印图像。</summary>
      <returns>打了水印的图像。</returns>
      <param name="watermarkImage">
        <see cref="T:System.Web.Helpers.WebImage" /> 对象。</param>
      <param name="width">水印图像的宽度（以像素为单位）。</param>
      <param name="height">水印图像的高度（以像素为单位）。</param>
      <param name="horizontalAlign">水印图像的水平对齐。值可以为“靠左”、“靠右”或“居中”。</param>
      <param name="verticalAlign">水印图像的垂直对齐。值可以为“靠上”、“居中”或“靠下”。</param>
      <param name="opacity">水印图像的不透明度，已指定为 0 和 100 之间的某个值。</param>
      <param name="padding">水印图像周围的边距的大小（以像素为单位）。</param>
    </member>
    <member name="M:System.Web.Helpers.WebImage.AddTextWatermark(System.String,System.String,System.Int32,System.String,System.String,System.String,System.String,System.Int32,System.Int32)">
      <summary>在图像中添加水印文本。</summary>
      <returns>打了水印的图像。</returns>
      <param name="text">要用作水印的文本。</param>
      <param name="fontColor">水印文本的颜色。</param>
      <param name="fontSize">水印文本的字体大小。</param>
      <param name="fontStyle">水印文本的字体样式。</param>
      <param name="fontFamily">水印文本的字体类型。</param>
      <param name="horizontalAlign">水印文本的水平对齐。值可以为“靠左”、“靠右”或“居中”。</param>
      <param name="verticalAlign">水印文本的垂直对齐。值可以为“靠上”、“居中”或“靠下”。</param>
      <param name="opacity">水印图像的不透明度，已指定为 0 和 100 之间的某个值。</param>
      <param name="padding">水印文本周围的边距的大小（以像素为单位）。</param>
    </member>
    <member name="M:System.Web.Helpers.WebImage.Clone">
      <summary>复制 <see cref="T:System.Web.Helpers.WebImage" /> 对象。</summary>
      <returns>图像。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebImage.Crop(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>剪切图像。</summary>
      <returns>剪切的图像。</returns>
      <param name="top">要从顶部删除的像素数。</param>
      <param name="left">要从左侧删除的像素数。</param>
      <param name="bottom">要从底部删除的像素数。</param>
      <param name="right">要从右侧删除的像素数。</param>
    </member>
    <member name="P:System.Web.Helpers.WebImage.FileName">
      <summary>获取或设置 <see cref="T:System.Web.Helpers.WebImage" /> 对象的文件名。</summary>
      <returns>文件名。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebImage.FlipHorizontal">
      <summary>水平翻转图像。</summary>
      <returns>翻转的图像。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebImage.FlipVertical">
      <summary>垂直翻转图像。</summary>
      <returns>翻转的图像。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebImage.GetBytes(System.String)">
      <summary>以字节数组形式返回图像。</summary>
      <returns>图像。</returns>
      <param name="requestedFormat">
        <see cref="T:System.Web.Helpers.WebImage" /> 对象的 <see cref="P:System.Web.Helpers.WebImage.ImageFormat" /> 值。</param>
    </member>
    <member name="M:System.Web.Helpers.WebImage.GetImageFromRequest(System.String)">
      <summary>返回已使用浏览器上载的图像。</summary>
      <returns>图像。</returns>
      <param name="postedFileName">（可选）已发布的文件的名称。如果未指定文件名，将返回第一个上载的文件。</param>
    </member>
    <member name="P:System.Web.Helpers.WebImage.Height">
      <summary>获取图像的高度（以像素为单位）。</summary>
      <returns>高度。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebImage.ImageFormat">
      <summary>获取图像的格式（例如，“jpeg”或“png”）。</summary>
      <returns>图像的文件格式。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebImage.Resize(System.Int32,System.Int32,System.Boolean,System.Boolean)">
      <summary>调整图像大小。</summary>
      <returns>已调整大小的图像。</returns>
      <param name="width">
        <see cref="T:System.Web.Helpers.WebImage" /> 对象的宽度（以像素为单位）。</param>
      <param name="height">
        <see cref="T:System.Web.Helpers.WebImage" /> 对象的高度（以像素为单位）。</param>
      <param name="preserveAspectRatio">若要保留图像的纵横比，则为 true；否则为 false。</param>
      <param name="preventEnlarge">若要防止放大图像，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Web.Helpers.WebImage.RotateLeft">
      <summary>将图像旋转到左侧。</summary>
      <returns>已旋转的图像。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebImage.RotateRight">
      <summary>将图像旋转到右侧。</summary>
      <returns>已旋转的图像。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebImage.Save(System.String,System.String,System.Boolean)">
      <summary>使用指定文件名保存图像。</summary>
      <returns>图像。</returns>
      <param name="filePath">用于保存图像的路径。</param>
      <param name="imageFormat">保存图像文件时要使用的格式，如“gif”或“png”。</param>
      <param name="forceCorrectExtension">若要对 <paramref name="imageFormat" /> 中指定的格式强制使用正确的文件名扩展名，则为 true；否则为 false。如果文件类型与指定文件名扩展名不匹配，且 <paramref name="forceCorrectExtension" /> 为 true，则会将正确的扩展名附加到文件名后面。例如，名为 Photograph.txt 的 PNG 文件将使用名称 Photograph.txt.png 进行保存。</param>
    </member>
    <member name="P:System.Web.Helpers.WebImage.Width">
      <summary>获取图像的宽度（以像素为单位）。</summary>
      <returns>宽度。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebImage.Write(System.String)">
      <summary>将图像呈现到浏览器。</summary>
      <returns>图像。</returns>
      <param name="requestedFormat">（可选）写入图像时要使用的文件格式。</param>
    </member>
    <member name="T:System.Web.Helpers.WebMail">
      <summary>提供使用简单邮件传输协议 (SMTP) 构建并发送电子邮件的方法。</summary>
    </member>
    <member name="P:System.Web.Helpers.WebMail.EnableSsl">
      <summary>获取或设置一个值，该值指示在发送电子邮件时是否使用安全套接字层 (SSL) 来加密连接。</summary>
      <returns>如果使用 SSL 来加密连接，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebMail.From">
      <summary>获取或设置发件人的电子邮件地址。</summary>
      <returns>发件人的电子邮件地址。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebMail.Password">
      <summary>获取或设置发件人的电子邮件帐户的密码。</summary>
      <returns>发件人的密码。</returns>
    </member>
    <member name="M:System.Web.Helpers.WebMail.Send(System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.IEnumerable{System.String},System.Boolean,System.Collections.Generic.IEnumerable{System.String},System.String,System.String,System.String,System.String,System.String)">
      <summary>将指定邮件发送到进行传递的 SMTP 服务器。</summary>
      <param name="to">收件人的电子邮件地址。使用分号 (;) 分隔多名收件人。</param>
      <param name="subject">电子邮件的主题行。</param>
      <param name="body">电子邮件的正文。如果 <paramref name="isBodyHtml" /> 为 true，则将正文中的 HTML 解释为标记。</param>
      <param name="from">（可选）邮件发件人的电子邮件地址；如果不指定发送人，则为 null。默认值为 null。</param>
      <param name="cc">（可选）向其发送邮件副本的其他收件人的电子邮件地址；如果没有其他收件人，则为 null。使用分号 (;) 分隔多名收件人。默认值为 null。</param>
      <param name="filesToAttach">（可选）文件名的集合，用于指定要附加到电子邮件中的文件；如果没有要附加的文件，则为 null。默认值为 null。</param>
      <param name="isBodyHtml">（可选）若为 true，则指定电子邮件正文为 HTML 格式；若为 false，则指示正文为纯文本格式。默认值为 true。</param>
      <param name="additionalHeaders">（可选）标头的集合，可添加到此电子邮件包含的正常 SMTP 标头中；如果不发送其他标头，则为 null。默认值为 null。</param>
      <param name="bcc">（可选）向其发送邮件“密送”副本的其他收件人的电子邮件地址；如果没有其他收件人，则为 null。使用分号 (;) 分隔多名收件人。默认值为 null。</param>
      <param name="contentEncoding">（可选）用于邮件正文的编码。可能值为 <see cref="T:System.Text.Encoding" /> 类的属性值，如 <see cref="P:System.Text.Encoding.UTF8" />。默认值为 null。</param>
      <param name="headerEncoding">（可选）用于邮件标题的编码。可能值为 <see cref="T:System.Text.Encoding" /> 类的属性值，如 <see cref="P:System.Text.Encoding.UTF8" />。默认值为 null。</param>
      <param name="priority">（可选）用于指定邮件优先级的值（“常规”、“低”、“高”）。默认值为“常规”。</param>
      <param name="replyTo">（可选）收件人回复邮件时将使用的电子邮件地址。默认值为 null，表示回复地址为 From 属性的值。</param>
    </member>
    <member name="P:System.Web.Helpers.WebMail.SmtpPort">
      <summary>获取或设置用于 SMTP 事务的端口。</summary>
      <returns>用于 SMTP 事务的端口。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebMail.SmtpServer">
      <summary>获取或设置用于传送电子邮件的 SMTP 服务器的名称。</summary>
      <returns>SMTP 服务器。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebMail.SmtpUseDefaultCredentials">
      <summary>获取或设置指示是否与请求一起发送默认凭据的值。</summary>
      <returns>如果与邮件一起发送凭据，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Web.Helpers.WebMail.UserName">
      <summary>获取或设置用于发送电子邮件的电子邮件帐户名。</summary>
      <returns>用户帐户的名称。</returns>
    </member>
  </members>
</doc>