﻿namespace Admin
{
    partial class 管理称号
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.dataGridView1 = new System.Windows.Forms.DataGridView();
            this.称号名 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.默认 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.称号效果 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.folderBrowserDialog1 = new System.Windows.Forms.FolderBrowserDialog();
            this.dataGridView2 = new System.Windows.Forms.DataGridView();
            this.button1 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.卡牌名 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.图标 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.道具 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.数量 = new System.Windows.Forms.DataGridViewTextBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView2)).BeginInit();
            this.SuspendLayout();
            // 
            // dataGridView1
            // 
            this.dataGridView1.AllowUserToAddRows = false;
            this.dataGridView1.AllowUserToDeleteRows = false;
            this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView1.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.称号名,
            this.默认,
            this.称号效果});
            this.dataGridView1.Location = new System.Drawing.Point(4, 3);
            this.dataGridView1.Name = "dataGridView1";
            this.dataGridView1.ReadOnly = true;
            this.dataGridView1.RowHeadersVisible = false;
            this.dataGridView1.RowTemplate.Height = 23;
            this.dataGridView1.Size = new System.Drawing.Size(404, 409);
            this.dataGridView1.TabIndex = 2;
            this.dataGridView1.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dataGridView1_CellClick);
            // 
            // 称号名
            // 
            this.称号名.HeaderText = "称号名";
            this.称号名.Name = "称号名";
            this.称号名.ReadOnly = true;
            this.称号名.Width = 150;
            // 
            // 默认
            // 
            this.默认.HeaderText = "默认";
            this.默认.Name = "默认";
            this.默认.ReadOnly = true;
            // 
            // 称号效果
            // 
            this.称号效果.HeaderText = "称号效果";
            this.称号效果.Name = "称号效果";
            this.称号效果.ReadOnly = true;
            this.称号效果.Width = 400;
            // 
            // dataGridView2
            // 
            this.dataGridView2.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridView2.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.卡牌名,
            this.图标,
            this.道具,
            this.数量});
            this.dataGridView2.Location = new System.Drawing.Point(414, 3);
            this.dataGridView2.Name = "dataGridView2";
            this.dataGridView2.RowHeadersVisible = false;
            this.dataGridView2.RowTemplate.Height = 23;
            this.dataGridView2.Size = new System.Drawing.Size(415, 409);
            this.dataGridView2.TabIndex = 2;
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(835, 12);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(93, 31);
            this.button1.TabIndex = 3;
            this.button1.Text = "保存卡牌设定";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(835, 49);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(93, 31);
            this.button2.TabIndex = 3;
            this.button2.Text = "保存成就设定";
            this.button2.UseVisualStyleBackColor = true;
            // 
            // 卡牌名
            // 
            this.卡牌名.HeaderText = "卡牌名";
            this.卡牌名.Name = "卡牌名";
            this.卡牌名.Resizable = System.Windows.Forms.DataGridViewTriState.True;
            this.卡牌名.Width = 150;
            // 
            // 图标
            // 
            this.图标.HeaderText = "图标";
            this.图标.Name = "图标";
            this.图标.Width = 80;
            // 
            // 道具
            // 
            this.道具.HeaderText = "道具";
            this.道具.Name = "道具";
            // 
            // 数量
            // 
            this.数量.HeaderText = "数量";
            this.数量.Name = "数量";
            this.数量.Width = 80;
            // 
            // 管理称号
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(935, 427);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.button1);
            this.Controls.Add(this.dataGridView2);
            this.Controls.Add(this.dataGridView1);
            this.Name = "管理称号";
            this.Text = "管理称号";
            this.Load += new System.EventHandler(this.管理称号_Load);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridView2)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.DataGridView dataGridView1;
        private System.Windows.Forms.DataGridViewTextBoxColumn 称号名;
        private System.Windows.Forms.DataGridViewTextBoxColumn 默认;
        private System.Windows.Forms.DataGridViewTextBoxColumn 称号效果;
        private System.Windows.Forms.FolderBrowserDialog folderBrowserDialog1;
        private System.Windows.Forms.DataGridView dataGridView2;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.DataGridViewTextBoxColumn 卡牌名;
        private System.Windows.Forms.DataGridViewTextBoxColumn 图标;
        private System.Windows.Forms.DataGridViewTextBoxColumn 道具;
        private System.Windows.Forms.DataGridViewTextBoxColumn 数量;
    }
}