using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using Newtonsoft.Json;
using PetShikongTools;
using Shikong.Pokemon2.PCG;

namespace Shikong.Pokemon2.PCG.成长突破
{
    /// <summary>
    /// 成长突破功能 - 管理成长突破等级
    /// </summary>
    public static class GrowthBreakthrough
    {

        /// <summary>
        /// 突破圣石道具ID
        /// </summary>
        public const string BREAKTHROUGH_STONE_ID = "2025060801";

        /// <summary>
        /// 凤凰晶石道具ID
        /// </summary>
        public const string PHOENIX_STONE_ID = "2025060802";

        /// <summary>
        /// 种族等级定义（数值越大等级越高）
        /// </summary>
        private static Dictionary<string, int> raceLevel = new Dictionary<string, int>
        {
            {"萌", 1},
            {"灵", 2},
            {"梦", 3},
            {"圣灵", 4}
        };

        /// <summary>
        /// 检查宠物种族是否满足突破要求
        /// </summary>
        /// <param name="petRace">宠物当前种族</param>
        /// <param name="requiredRace">要求的最低种族</param>
        /// <returns>是否满足要求</returns>
        private static bool CheckRaceRequirement(string petRace, string requiredRace)
        {
            // 圣灵可以满足所有要求
            if (petRace == "圣灵")
                return true;
                
            // 获取种族等级
            if (!raceLevel.ContainsKey(petRace) || !raceLevel.ContainsKey(requiredRace))
                return false;
                
            // 当前种族等级必须大于等于要求的种族等级
            return raceLevel[petRace] >= raceLevel[requiredRace];
        }



        /// <summary>
        /// 尝试进行成长突破
        /// </summary>
        /// <param name="petId">宠物序号</param>
        /// <param name="phoenixStoneCount">使用的凤凰晶石数量(0-3)</param>
        /// <returns>突破结果信息</returns>
        public static string TryBreakthrough(string petId = null, int phoenixStoneCount = 0)
        {
            var dataProcess = new DataProcess();
            var user = dataProcess.ReadUserInfo();
            
            // 如果没有指定宠物，则使用主宠物
            if (string.IsNullOrEmpty(petId))
            {
                petId = user.主宠物;
            }
            
            var targetPet = dataProcess.ReadAppointedPet(petId);

            // 检查神圣结界等级（这里假设需要满级，具体可根据能量汇聚系统调整）
            // if (user.神圣结界等级 < 100)
            // {
            //     return "神圣结界未满级，无法进行成长突破！";
            // }

            // 获取宠物当前突破等级
            int currentLevel = string.IsNullOrEmpty(targetPet.成长突破等级) ? 0 : Convert.ToInt32(targetPet.成长突破等级);

            if (currentLevel >= BreakthroughConfigManager.GetMaxLevel())
            {
                return "该宠物成长突破已达到最高等级！";
            }

            // 获取下一级配置
            var nextConfig = BreakthroughConfigManager.GetConfig(currentLevel + 1);
            if (nextConfig == null)
            {
                return "找不到突破配置！";
            }

            // 检查宠物
            if (targetPet == null)
            {
                return "找不到指定的宠物！";
            }

            // 检查成长值要求
            double currentCC = Convert.ToDouble(targetPet.成长);
            if (currentCC < nextConfig.ccRequired * 10000) // 配置中是万为单位
            {
                return $"{targetPet.宠物名字}成长需要达到{nextConfig.ccRequired}万才能进行{nextConfig.level}阶突破！当前成长：{(int)(currentCC/10000)}万";
            }

            // 检查种族要求
            if (!CheckRaceRequirement(targetPet.五行, nextConfig.raceRequired))
            {
                return $"{targetPet.宠物名字}种族需要达到{nextConfig.raceRequired}族才能进行{nextConfig.level}阶突破！当前种族：{targetPet.五行}";
            }

            // 检查突破圣石
            var breakStone = dataProcess.GetAP_ID(BREAKTHROUGH_STONE_ID);
            if (breakStone == null || Convert.ToInt32(breakStone.道具数量) < nextConfig.stoneRequired)
            {
                return $"突破圣石不足！需要{nextConfig.stoneRequired}个，当前拥有{(breakStone?.道具数量 ?? "0")}个";
            }

            // 检查凤凰晶石
            if (phoenixStoneCount > 0)
            {
                if (phoenixStoneCount > 3)
                {
                    phoenixStoneCount = 3; // 最多使用3个
                }

                var phoenixStone = dataProcess.GetAP_ID(PHOENIX_STONE_ID);
                if (phoenixStone == null || Convert.ToInt32(phoenixStone.道具数量) < phoenixStoneCount)
                {
                    return $"凤凰晶石不足！需要{phoenixStoneCount}个，当前拥有{(phoenixStone?.道具数量 ?? "0")}个";
                }
            }

            // 计算成功率（BreakthroughConfig中的baseSuccessRate是整数百分比）
            double successRate = nextConfig.baseSuccessRate / 100.0;
            if (!string.IsNullOrEmpty(targetPet.成长突破累计成功率))
            {
                successRate += Convert.ToDouble(targetPet.成长突破累计成功率) / 100.0; // 失败叠加的成功率
            }
            successRate += phoenixStoneCount * 0.05; // 凤凰晶石加成

            // 确保成功率不超过100%
            if (successRate > 1.0)
                successRate = 1.0;

            // 消耗材料
            dataProcess.ReducePropNum_ID(BREAKTHROUGH_STONE_ID, nextConfig.stoneRequired);
            if (phoenixStoneCount > 0)
            {
                dataProcess.ReducePropNum_ID(PHOENIX_STONE_ID, phoenixStoneCount);
            }

            // 判断是否成功
            bool isSuccess = Shikong.Pokemon2.PCG.DataProcess.RandomNext(0, 100, 2) < successRate * 100;

            if (isSuccess)
            {
                // 突破成功
                // 折损成长值
                double newCC = currentCC * (1 - nextConfig.ccLossRate);
                targetPet.成长 = newCC.ToString();

                // 设置新的成长上限
                targetPet.成长上限 = (nextConfig.ccMax * 10000).ToString();

                // 更新宠物突破等级
                targetPet.成长突破等级 = (currentLevel + 1).ToString();

                // 重置宠物累计成功率
                targetPet.成长突破累计成功率 = "0";

                // 保存宠物数据
                dataProcess.Update_APDF(targetPet.宠物序号, targetPet);

                Shikong.Pokemon2.PCG.DataProcess.GameForm.发送游戏公告($"【成长突破】恭喜玩家[{user.名字}]的{targetPet.宠物名字}成功突破至{currentLevel + 1}阶，成长上限提升至{nextConfig.ccMax}万！");

                return $"突破成功！{targetPet.宠物名字}成功突破至{currentLevel + 1}阶！\n成长值：{(int)(currentCC/10000)}万 → {(int)(newCC/10000)}万\n成长上限提升至{nextConfig.ccMax}万！";
            }
            else
            {
                // 突破失败
                // 增加宠物累计成功率
                int currentAccumulated = string.IsNullOrEmpty(targetPet.成长突破累计成功率) ? 0 : Convert.ToInt32(targetPet.成长突破累计成功率);
                targetPet.成长突破累计成功率 = (currentAccumulated + 1).ToString();

                // 保存宠物数据
                dataProcess.Update_APDF(targetPet.宠物序号, targetPet);

                return $"{targetPet.宠物名字}突破失败！累计失败成功率加成：{targetPet.成长突破累计成功率}%";
            }
        }

        /// <summary>
        /// 获取成长突破信息
        /// </summary>
        /// <param name="petId">宠物序号，如果为空则使用主宠物</param>
        /// <returns>JSON格式的突破信息</returns>
        public static string GetBreakthroughInfo(string petId = null)
        {
            var dataProcess = new DataProcess();
            var user = dataProcess.ReadUserInfo();

            // 如果没有指定宠物，则使用主宠物
            if (string.IsNullOrEmpty(petId))
            {
                petId = user.主宠物;
            }

            var targetPet = dataProcess.ReadAppointedPet(petId);

            if (targetPet == null)
            {
                return JsonConvert.SerializeObject(new { error = "找不到指定的宠物！" });
            }

            int currentLevel = string.IsNullOrEmpty(targetPet.成长突破等级) ? 0 : Convert.ToInt32(targetPet.成长突破等级);
            var nextBreakthrough = currentLevel < BreakthroughConfigManager.GetMaxLevel() ? BreakthroughConfigManager.GetConfig(currentLevel + 1) : null;

            var info = new
            {
                petId = targetPet.宠物序号,
                petName = targetPet.宠物名字,
                petRace = targetPet.五行,
                currentLevel = currentLevel,
                maxLevel = BreakthroughConfigManager.GetMaxLevel(),
                accumulatedSuccessRate = string.IsNullOrEmpty(targetPet.成长突破累计成功率) ? 0 : Convert.ToInt32(targetPet.成长突破累计成功率),
                currentCC = (int)(Convert.ToDouble(targetPet.成长) / 10000),
                currentCCMax = (int)(Convert.ToDouble(targetPet.成长上限) / 10000),
                barrierLevel = string.IsNullOrEmpty(user.神圣结界等级) ? 0 : Convert.ToInt32(user.神圣结界等级), // 神圣结界等级还是用户级别的
                nextBreakthrough = nextBreakthrough
            };

            return JsonConvert.SerializeObject(info);
        }

        /// <summary>
        /// 获取所有突破配置
        /// </summary>
        /// <returns>JSON格式的所有配置</returns>
        public static string GetAllConfigs()
        {
            return JsonConvert.SerializeObject(BreakthroughConfigManager.GetAllConfigs());
        }

        /// <summary>
        /// 获取所有宠物的成长突破信息摘要
        /// </summary>
        /// <returns>JSON格式的宠物突破信息列表</returns>
        public static string GetAllPetsBreakthroughSummary()
        {
            var dataProcess = new DataProcess();
            var allPets = dataProcess.ReadPlayerPetList();

            var petSummaries = allPets.Select(pet => new
            {
                petId = pet.宠物序号,
                petName = pet.宠物名字,
                petRace = pet.五行,
                currentLevel = string.IsNullOrEmpty(pet.成长突破等级) ? 0 : Convert.ToInt32(pet.成长突破等级),
                currentCC = (int)(Convert.ToDouble(pet.成长) / 10000),
                currentCCMax = (int)(Convert.ToDouble(pet.成长上限) / 10000),
                accumulatedSuccessRate = string.IsNullOrEmpty(pet.成长突破累计成功率) ? 0 : Convert.ToInt32(pet.成长突破累计成功率)
            }).ToList();

            return JsonConvert.SerializeObject(petSummaries);
        }

        /// <summary>
        /// 处理成长突破相关的命令
        /// </summary>
        /// <param name="command">命令字符串</param>
        /// <returns>处理结果</returns>
        public static string ProcessCommand(string command)
        {
            string[] parts = command.Split(' ');

            if (parts[0] == "/成长突破")
            {
                if (parts.Length == 1)
                {
                    // 显示主宠物信息
                    return GetBreakthroughInfo();
                }
                else if (parts[1] == "查看")
                {
                    if (parts.Length == 2)
                    {
                        // 显示所有配置
                        return GetAllConfigs();
                    }
                    else if (parts.Length == 3)
                    {
                        // 查看指定宠物信息：/成长突破 查看 宠物序号
                        return GetBreakthroughInfo(parts[2]);
                    }
                }
                else if (parts[1] == "宠物列表")
                {
                    // 显示所有宠物的突破信息摘要
                    return GetAllPetsBreakthroughSummary();
                }
                else if (parts[1] == "执行")
                {
                    // 执行突破
                    string petId = null;
                    int phoenixStoneCount = 0;

                    if (parts.Length > 2)
                    {
                        // 第一个参数可能是宠物ID或凤凰晶石数量
                        if (int.TryParse(parts[2], out phoenixStoneCount))
                        {
                            // 是数字，表示凤凰晶石数量：/成长突破 执行 3
                            petId = null; // 使用主宠物
                        }
                        else
                        {
                            // 不是数字，表示宠物ID：/成长突破 执行 宠物序号 [凤凰晶石数量]
                            petId = parts[2];
                            if (parts.Length > 3 && int.TryParse(parts[3], out phoenixStoneCount))
                            {
                                // 有凤凰晶石数量参数
                            }
                            else
                            {
                                phoenixStoneCount = 0;
                            }
                        }
                    }

                    return TryBreakthrough(petId, phoenixStoneCount);
                }
            }

            return "未知的成长突破命令！\n" +
                   "用法：\n" +
                   "/成长突破 - 查看主宠物突破信息\n" +
                   "/成长突破 查看 - 查看所有突破配置\n" +
                   "/成长突破 查看 宠物序号 - 查看指定宠物突破信息\n" +
                   "/成长突破 宠物列表 - 查看所有宠物突破信息摘要\n" +
                   "/成长突破 执行 [凤凰晶石数量] - 主宠物突破\n" +
                   "/成长突破 执行 宠物序号 [凤凰晶石数量] - 指定宠物突破";
        }
    }
}
