﻿using Newtonsoft.Json;
using PetShikongTools;
using Shikong.Pokemon2.PCG;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Admin
{
    class T
    {
        private const string VKey = "HYFMWZS";
        private const string Key11 = "XGVUSZF";
        private const string Key12 = "LjIwMTYuMjAxNy4yMDE4LjIwMjAuMjAyMS4yMDIy";
        private const string Key21 = "JYSWQRT";
        private const string Key22 = "LjEyMzQuNTY3OC45MDEyLjM0NTYuNzg5MC5hYmNk";
        public static PropType GetAPType(string 道具序号) //取指定道具类型
        {
            List<PropType> 道具 = new DataProcess().ReadAllPropTypes();
            return 道具.FirstOrDefault(信息 => 信息.道具序号.Equals(道具序号));
        }
        public static bool AddTaskAim(TaskInfo 任务, string 任务说明)
        {
            List<TaskInfo> 列表 = new DataProcess().GetAllTaskAim();
            if (列表.Any(信息 => 信息.任务序号 == 任务.任务序号))
            {
                return false;
            }

            new DataProcess().SaveFile(SkRC4.DES.EncryptRC4(任务说明, GetKey(1)), DataProcess.TDC_Path + @"/" + 任务.任务序号 + "_.dat");
            列表.Add(任务);
            SaveTaskAim(列表);
        
            return true;
        }
        public static MonsterType getMInfo(String name) {
            var mL = new DataProcess().Get_MTList();
            var mInfo = mL.FirstOrDefault(C => C.怪物名字.Equals(name));
            //var mInfo = mL.FirstOrDefault(C => C.怪物名字.Contains(name));
            return mInfo;
        }
        public static PropType getPInfo(String name)
        {
            var pL = new DataProcess().ReadAllPropTypes();
            var pInfo = pL.FirstOrDefault(C => C.道具名字.Contains(name));
            return pInfo;
        }
        public static PropType getPInfo1(String name)
        {
            var pL = new DataProcess().ReadAllPropTypes();
            var pInfo = pL.FirstOrDefault(C => C.道具名字 == name);
            return pInfo;
        }
        public static void SaveTaskAim(List<TaskInfo> tasklist)
        {
            string cfg = JsonConvert.SerializeObject(tasklist);
            cfg = SkRC4.DES.EncryptRC4(cfg, GetKey(1));
            new DataProcess().SaveFile(cfg, DataProcess.TDC_Path + @"\_0.task");
        }
        public static string GetKey(int keyIndex, bool dat = false)
        {
            if (keyIndex == 1)
            {
                if (dat)
                {
                    return SkCryptography.Vigenere.de(Key11, VKey).ToLower() + SkCryptography.Base64.DecodeBase64(Key12) + "ZNQMCK";

                }
                return SkCryptography.Vigenere.de(Key11, VKey).ToLower() + SkCryptography.Base64.DecodeBase64(Key12);
            }

            if (keyIndex == 2)
            {
                return SkCryptography.Vigenere.de(Key21, VKey).ToLower() + SkCryptography.Base64.DecodeBase64(Key22);
            }

            return null;
        }

    }
}
